// Simplified version without code generation
// import 'package:json_annotation/json_annotation.dart';
// part 'user.g.dart';
// @JsonSerializable()
class User {
  final int id;
  final String username;
  final String email;
  final int coins;
  final int gems;
  final int level;
  final int xp;
  @Json<PERSON>ey(name: 'createdAt')
  final DateTime? createdAt;
  @Json<PERSON>ey(name: 'lastLogin')
  final DateTime? lastLogin;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.coins,
    required this.gems,
    required this.level,
    required this.xp,
    this.createdAt,
    this.lastLogin,
  });

  factory User.fromJson(Map<String, dynamic> json) => _$UserFromJson(json);
  Map<String, dynamic> toJson() => _$UserToJson(this);

  User copyWith({
    int? id,
    String? username,
    String? email,
    int? coins,
    int? gems,
    int? level,
    int? xp,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      coins: coins ?? this.coins,
      gems: gems ?? this.gems,
      level: level ?? this.level,
      xp: xp ?? this.xp,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }

  // Calculate XP needed for next level (simplified)
  int get xpToNextLevel {
    final nextLevelXP = (level + 1) * 100; // Simple formula
    return nextLevelXP - xp;
  }

  // Calculate XP progress percentage
  double get xpProgress {
    final currentLevelXP = level * 100;
    final nextLevelXP = (level + 1) * 100;
    final progressXP = xp - currentLevelXP;
    final totalXPNeeded = nextLevelXP - currentLevelXP;
    return progressXP / totalXPNeeded;
  }

  @override
  String toString() {
    return 'User(id: $id, username: $username, level: $level, coins: $coins, gems: $gems)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is User && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class UserStats {
  @JsonKey(name: 'rallyStats')
  final RallyStats rallyStats;
  @JsonKey(name: 'cardStats')
  final CardStats cardStats;
  final int level;
  final int xp;
  @JsonKey(name: 'xpToNext')
  final int xpToNext;

  const UserStats({
    required this.rallyStats,
    required this.cardStats,
    required this.level,
    required this.xp,
    required this.xpToNext,
  });

  factory UserStats.fromJson(Map<String, dynamic> json) => _$UserStatsFromJson(json);
  Map<String, dynamic> toJson() => _$UserStatsToJson(this);
}

@JsonSerializable()
class RallyStats {
  @JsonKey(name: 'total_rallies')
  final int totalRallies;
  @JsonKey(name: 'avg_position')
  final double? avgPosition;
  @JsonKey(name: 'best_position')
  final int? bestPosition;
  @JsonKey(name: 'total_points')
  final int totalPoints;
  @JsonKey(name: 'best_time')
  final int? bestTime;

  const RallyStats({
    required this.totalRallies,
    this.avgPosition,
    this.bestPosition,
    required this.totalPoints,
    this.bestTime,
  });

  factory RallyStats.fromJson(Map<String, dynamic> json) => _$RallyStatsFromJson(json);
  Map<String, dynamic> toJson() => _$RallyStatsToJson(this);

  // Format best time for display
  String get formattedBestTime {
    if (bestTime == null) return 'N/A';
    final minutes = bestTime! ~/ 60000;
    final seconds = (bestTime! % 60000) ~/ 1000;
    final milliseconds = bestTime! % 1000;
    return '$minutes:${seconds.toString().padLeft(2, '0')}.${milliseconds.toString().padLeft(3, '0')}';
  }
}

@JsonSerializable()
class CardStats {
  @JsonKey(name: 'total_cards')
  final int totalCards;
  @JsonKey(name: 'legendary_cards')
  final int legendaryCards;
  @JsonKey(name: 'epic_cards')
  final int epicCards;
  @JsonKey(name: 'rare_cards')
  final int rareCards;
  @JsonKey(name: 'common_cards')
  final int commonCards;

  const CardStats({
    required this.totalCards,
    required this.legendaryCards,
    required this.epicCards,
    required this.rareCards,
    required this.commonCards,
  });

  factory CardStats.fromJson(Map<String, dynamic> json) => _$CardStatsFromJson(json);
  Map<String, dynamic> toJson() => _$CardStatsToJson(this);

  // Get card count by rarity
  int getCardCount(String rarity) {
    switch (rarity.toLowerCase()) {
      case 'legendary':
        return legendaryCards;
      case 'epic':
        return epicCards;
      case 'rare':
        return rareCards;
      case 'common':
        return commonCards;
      default:
        return 0;
    }
  }

  // Calculate collection completion percentage
  double get completionPercentage {
    // This would need to be calculated based on total available cards
    // For now, return a simple calculation
    return totalCards / 100.0; // Assuming 100 total cards available
  }
}
