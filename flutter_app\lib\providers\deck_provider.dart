import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/card.dart';
import '../services/api_service.dart';

// Deck provider
final deckProvider = StateNotifierProvider<DeckNotifier, DeckState>((ref) {
  return DeckNotifier();
});

// Current deck provider
final currentDeckProvider = Provider<Deck?>((ref) {
  final deckState = ref.watch(deckProvider);
  return deckState.currentDeck;
});

// Deck validation provider
final deckValidationProvider = Provider<DeckValidation>((ref) {
  final deckState = ref.watch(deckProvider);
  return DeckValidation.validate(deckState.currentDeck);
});

// Deck State
class DeckState {
  final List<Deck> decks;
  final Deck? currentDeck;
  final bool isLoading;
  final String? error;

  const DeckState({
    this.decks = const [],
    this.currentDeck,
    this.isLoading = false,
    this.error,
  });

  DeckState copyWith({
    List<Deck>? decks,
    Deck? currentDeck,
    bool? isLoading,
    String? error,
  }) {
    return DeckState(
      decks: decks ?? this.decks,
      currentDeck: currentDeck,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Deck Notifier
class DeckNotifier extends StateNotifier<DeckState> {
  DeckNotifier() : super(const DeckState()) {
    _initializeDefaultDeck();
  }

  void _initializeDefaultDeck() {
    // Create a default deck for testing
    final defaultDeck = Deck(
      id: 1,
      name: 'Default Deck',
      driverCardId: 1,
      codriverCardId: 2,
      vehicleCardId: 3,
      strategyCards: [4, 5],
      isActive: true,
      createdAt: DateTime.now(),
    );

    state = state.copyWith(
      decks: [defaultDeck],
      currentDeck: defaultDeck,
    );
  }

  Future<void> loadDecks() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // TODO: Implement API call
      // For now, use mock data
      await Future.delayed(const Duration(milliseconds: 500));
      
      final mockDecks = [
        Deck(
          id: 1,
          name: 'Speed Demon',
          driverCardId: 1,
          codriverCardId: 2,
          vehicleCardId: 3,
          strategyCards: [4, 5],
          isActive: true,
          createdAt: DateTime.now(),
        ),
        Deck(
          id: 2,
          name: 'All-Rounder',
          driverCardId: 6,
          codriverCardId: 7,
          vehicleCardId: 8,
          strategyCards: [9, 10],
          isActive: false,
          createdAt: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ];

      state = state.copyWith(
        decks: mockDecks,
        currentDeck: state.currentDeck ?? mockDecks.first,
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load decks: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  void selectDeck(Deck deck) {
    state = state.copyWith(currentDeck: deck);
  }

  Future<void> saveDeck(Deck deck) async {
    try {
      // TODO: Implement API call
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedDecks = [...state.decks];
      final existingIndex = updatedDecks.indexWhere((d) => d.id == deck.id);
      
      if (existingIndex >= 0) {
        updatedDecks[existingIndex] = deck;
      } else {
        updatedDecks.add(deck);
      }

      state = state.copyWith(
        decks: updatedDecks,
        currentDeck: deck,
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to save deck: ${e.toString()}');
    }
  }

  Future<void> deleteDeck(int deckId) async {
    try {
      // TODO: Implement API call
      await Future.delayed(const Duration(milliseconds: 500));

      final updatedDecks = state.decks.where((d) => d.id != deckId).toList();
      Deck? newCurrentDeck = state.currentDeck;
      
      if (state.currentDeck?.id == deckId) {
        newCurrentDeck = updatedDecks.isNotEmpty ? updatedDecks.first : null;
      }

      state = state.copyWith(
        decks: updatedDecks,
        currentDeck: newCurrentDeck,
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to delete deck: ${e.toString()}');
    }
  }

  void updateDeckCard(String cardType, int cardId) {
    if (state.currentDeck == null) return;

    final currentDeck = state.currentDeck!;
    Deck updatedDeck;

    switch (cardType.toLowerCase()) {
      case 'driver':
        updatedDeck = currentDeck.copyWith(driverCardId: cardId);
        break;
      case 'codriver':
        updatedDeck = currentDeck.copyWith(codriverCardId: cardId);
        break;
      case 'vehicle':
        updatedDeck = currentDeck.copyWith(vehicleCardId: cardId);
        break;
      case 'strategy':
        final newStrategyCards = [...currentDeck.strategyCards];
        if (newStrategyCards.length < 2) {
          newStrategyCards.add(cardId);
        } else {
          // Replace the first strategy card
          newStrategyCards[0] = cardId;
        }
        updatedDeck = currentDeck.copyWith(strategyCards: newStrategyCards);
        break;
      default:
        return;
    }

    state = state.copyWith(currentDeck: updatedDeck);
  }

  void removeStrategyCard(int cardId) {
    if (state.currentDeck == null) return;

    final currentDeck = state.currentDeck!;
    final newStrategyCards = currentDeck.strategyCards.where((id) => id != cardId).toList();
    final updatedDeck = currentDeck.copyWith(strategyCards: newStrategyCards);

    state = state.copyWith(currentDeck: updatedDeck);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Deck Validation
class DeckValidation {
  final bool isValid;
  final List<String> errors;
  final List<String> warnings;

  const DeckValidation({
    required this.isValid,
    this.errors = const [],
    this.warnings = const [],
  });

  static DeckValidation validate(Deck? deck) {
    if (deck == null) {
      return const DeckValidation(
        isValid: false,
        errors: ['No deck selected'],
      );
    }

    final errors = <String>[];
    final warnings = <String>[];

    // Check required cards
    if (deck.driverCardId == 0) {
      errors.add('Driver card is required');
    }
    if (deck.codriverCardId == 0) {
      errors.add('Co-driver card is required');
    }
    if (deck.vehicleCardId == 0) {
      errors.add('Vehicle card is required');
    }

    // Check strategy cards
    if (deck.strategyCards.isEmpty) {
      warnings.add('No strategy cards selected');
    } else if (deck.strategyCards.length < 2) {
      warnings.add('Consider adding more strategy cards');
    }

    // Check for duplicate cards
    final allCardIds = [
      deck.driverCardId,
      deck.codriverCardId,
      deck.vehicleCardId,
      ...deck.strategyCards,
    ].where((id) => id != 0).toList();

    final uniqueCardIds = allCardIds.toSet();
    if (allCardIds.length != uniqueCardIds.length) {
      errors.add('Duplicate cards are not allowed');
    }

    return DeckValidation(
      isValid: errors.isEmpty,
      errors: errors,
      warnings: warnings,
    );
  }
}

// Rally participation provider
final rallyParticipationProvider = StateNotifierProvider<RallyParticipationNotifier, RallyParticipationState>((ref) {
  return RallyParticipationNotifier();
});

// Rally Participation State
class RallyParticipationState {
  final bool isParticipating;
  final String? currentRallyId;
  final Map<String, dynamic>? rallyProgress;
  final bool isLoading;
  final String? error;

  const RallyParticipationState({
    this.isParticipating = false,
    this.currentRallyId,
    this.rallyProgress,
    this.isLoading = false,
    this.error,
  });

  RallyParticipationState copyWith({
    bool? isParticipating,
    String? currentRallyId,
    Map<String, dynamic>? rallyProgress,
    bool? isLoading,
    String? error,
  }) {
    return RallyParticipationState(
      isParticipating: isParticipating ?? this.isParticipating,
      currentRallyId: currentRallyId,
      rallyProgress: rallyProgress,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Rally Participation Notifier
class RallyParticipationNotifier extends StateNotifier<RallyParticipationState> {
  RallyParticipationNotifier() : super(const RallyParticipationState());

  Future<RallyResult?> joinRally(String rallyId, Deck deck) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      // TODO: Implement API call
      await Future.delayed(const Duration(seconds: 2));

      // Simulate rally result
      final result = _simulateRallyResult(rallyId, deck);

      state = state.copyWith(
        isParticipating: false,
        isLoading: false,
      );

      return result;
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to join rally: ${e.toString()}',
        isLoading: false,
      );
      return null;
    }
  }

  RallyResult _simulateRallyResult(String rallyId, Deck deck) {
    // Simple simulation based on deck composition
    final random = DateTime.now().millisecond;
    final position = 1 + (random % 10); // Position 1-10
    final timeMs = 180000 + (random % 60000); // 3-4 minutes
    
    // Calculate rewards based on position
    int coinsEarned = 0;
    int xpEarned = 0;
    
    switch (position) {
      case 1:
        coinsEarned = 200;
        xpEarned = 100;
        break;
      case 2:
        coinsEarned = 150;
        xpEarned = 80;
        break;
      case 3:
        coinsEarned = 100;
        xpEarned = 60;
        break;
      default:
        coinsEarned = 50;
        xpEarned = 30;
    }

    return RallyResult(
      rallyId: rallyId,
      position: position,
      totalParticipants: 10,
      timeMs: timeMs,
      coinsEarned: coinsEarned,
      xpEarned: xpEarned,
      isPersonalBest: random % 3 == 0,
    );
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Rally Result
class RallyResult {
  final String rallyId;
  final int position;
  final int totalParticipants;
  final int timeMs;
  final int coinsEarned;
  final int xpEarned;
  final bool isPersonalBest;

  const RallyResult({
    required this.rallyId,
    required this.position,
    required this.totalParticipants,
    required this.timeMs,
    required this.coinsEarned,
    required this.xpEarned,
    required this.isPersonalBest,
  });

  String get formattedTime {
    final minutes = timeMs ~/ 60000;
    final seconds = (timeMs % 60000) ~/ 1000;
    final milliseconds = timeMs % 1000;
    return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}.${(milliseconds ~/ 10).toString().padLeft(2, '0')}';
  }

  String get positionSuffix {
    switch (position) {
      case 1:
        return 'st';
      case 2:
        return 'nd';
      case 3:
        return 'rd';
      default:
        return 'th';
    }
  }
}
