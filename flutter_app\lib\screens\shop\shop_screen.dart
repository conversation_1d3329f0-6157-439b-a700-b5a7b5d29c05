import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/shop_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/shop_item_widget.dart';
import '../../widgets/custom_button.dart';
import '../../models/shop.dart';
import 'purchase_dialog.dart';
import 'daily_bonus_dialog.dart';

class ShopScreen extends ConsumerStatefulWidget {
  const ShopScreen({super.key});

  @override
  ConsumerState<ShopScreen> createState() => _ShopScreenState();
}

class _ShopScreenState extends ConsumerState<ShopScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isGridView = true;

  // Mock shop items for demonstration
  final List<ShopItem> _mockItems = [
    const ShopItem(
      id: 1,
      name: 'Bronze Pack',
      type: 'card_pack',
      priceCoins: 100,
      priceGems: 0,
      priceRealMoney: 0.99,
      contents: {'cards': 3},
      description: '3 cards with guaranteed rare+',
    ),
    const ShopItem(
      id: 2,
      name: 'Silver Pack',
      type: 'card_pack',
      priceCoins: 250,
      priceGems: 5,
      priceRealMoney: 2.49,
      contents: {'cards': 5},
      description: '5 cards with guaranteed epic+',
      discountPercentage: 20,
    ),
    const ShopItem(
      id: 3,
      name: 'Gold Pack',
      type: 'card_pack',
      priceCoins: 500,
      priceGems: 10,
      priceRealMoney: 4.99,
      contents: {'cards': 7},
      description: '7 cards with guaranteed legendary',
    ),
    const ShopItem(
      id: 4,
      name: 'Legendary Pack',
      type: 'card_pack',
      priceCoins: 1000,
      priceGems: 25,
      priceRealMoney: 9.99,
      contents: {'cards': 10},
      description: '10 cards with multiple legendaries',
      limitedQuantity: 5,
    ),
    const ShopItem(
      id: 5,
      name: 'Coin Bundle',
      type: 'currency',
      priceCoins: 0,
      priceGems: 0,
      priceRealMoney: 1.99,
      contents: {'coins': 500},
      description: '500 coins for purchases',
    ),
    const ShopItem(
      id: 6,
      name: 'Gem Bundle',
      type: 'currency',
      priceCoins: 0,
      priceGems: 0,
      priceRealMoney: 4.99,
      contents: {'gems': 50},
      description: '50 gems for premium items',
      discountPercentage: 30,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load shop items on init
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // For demo, we'll use mock data
      // ref.read(shopItemsProvider.notifier).loadShopItems();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);
    final dailyBonusState = ref.watch(dailyBonusProvider);

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(user),
            
            // Daily bonus banner
            if (dailyBonusState.canClaim)
              _buildDailyBonusCard(),
            
            // Tab bar
            _buildTabBar(),
            
            // Content
            Expanded(
              child: _buildShopContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(user) {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Shop',
                      style: AppTheme.headingMedium.copyWith(
                        color: Colors.white,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Get new cards and boost your collection',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              IconButton(
                onPressed: () {
                  setState(() {
                    _isGridView = !_isGridView;
                  });
                },
                icon: Icon(
                  _isGridView ? Icons.view_list : Icons.grid_view,
                  color: Colors.white70,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          // Currency display
          Row(
            children: [
              Expanded(
                child: _buildCurrencyCard(
                  icon: Icons.monetization_on,
                  label: 'Coins',
                  value: user?.coins.toString() ?? '0',
                  color: AppTheme.warningColor,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildCurrencyCard(
                  icon: Icons.diamond,
                  label: 'Gems',
                  value: user?.gems.toString() ?? '0',
                  color: AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrencyCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: color.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 8),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.white70,
                  fontSize: 10,
                ),
              ),
              Text(
                value,
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDailyBonusCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: GestureDetector(
        onTap: () => _showDailyBonusDialog(),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            gradient: const LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.accentColor],
            ),
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            boxShadow: [
              BoxShadow(
                color: AppTheme.primaryColor.withOpacity(0.3),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              const Icon(
                Icons.card_giftcard,
                color: Colors.white,
                size: 32,
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Daily Bonus Available!',
                      style: AppTheme.bodyMedium.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    Text(
                      'Claim your free card and coins',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: Colors.white,
                size: 16,
              ),
            ],
          ),
        ),
      ).animate().shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3)),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppTheme.primaryColor,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.white54,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTheme.bodySmall,
        tabs: const [
          Tab(text: 'ALL'),
          Tab(text: 'CARD PACKS'),
          Tab(text: 'CURRENCY'),
          Tab(text: 'SPECIAL'),
        ],
      ),
    );
  }

  Widget _buildShopContent() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildItemGrid(_mockItems),
        _buildItemGrid(_mockItems.where((item) => item.isCardPack).toList()),
        _buildItemGrid(_mockItems.where((item) => item.isCurrency).toList()),
        _buildItemGrid(_mockItems.where((item) => item.limitedQuantity != null).toList()),
      ],
    );
  }

  Widget _buildItemGrid(List<ShopItem> items) {
    if (items.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: _isGridView ? _buildGridView(items) : _buildListView(items),
    );
  }

  Widget _buildGridView(List<ShopItem> items) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.75,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return ShopItemWidget(
          item: item,
          onTap: () => _showPurchaseDialog(item),
        ).animate().fadeIn(
          delay: (index * 100).ms,
          duration: 300.ms,
        ).slideY(
          begin: 0.3,
          end: 0,
        );
      },
    );
  }

  Widget _buildListView(List<ShopItem> items) {
    return ListView.builder(
      itemCount: items.length,
      itemBuilder: (context, index) {
        final item = items[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: CompactShopItemWidget(
            item: item,
            onTap: () => _showPurchaseDialog(item),
          ).animate().fadeIn(
            delay: (index * 50).ms,
            duration: 200.ms,
          ).slideX(
            begin: -0.3,
            end: 0,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.shopping_bag_outlined,
              size: 64,
              color: Colors.white38,
            ),
            const SizedBox(height: 16),
            Text(
              'No items available',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Check back later for new items',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showPurchaseDialog(ShopItem item) {
    showDialog(
      context: context,
      builder: (context) => PurchaseDialog(item: item),
    );
  }

  void _showDailyBonusDialog() {
    showDialog(
      context: context,
      builder: (context) => const DailyBonusDialog(),
    );
  }
}
