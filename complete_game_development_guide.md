# Rally Card Game - Complete Development Guide

## 🎯 Game Overview

### Core Concept
**Rally Championship Manager** is a collectible card game where players build rally teams by collecting driver, co-driver, and vehicle cards. Players compete in rally championships by strategically selecting their deck and making tactical decisions during races.

### Target Audience
- Mobile gamers who enjoy card collection games
- Rally/motorsport enthusiasts
- Strategy game players
- Ages 13+ (card game mechanics appeal to teens and adults)

---

## 🎮 Detailed Gameplay Mechanics

### Core Game Loop
1. **Collection Phase**: Open card packs, build collection
2. **Team Building**: Create rally deck (15-20 cards)
3. **Rally Selection**: Choose from available championship events
4. **Stage Racing**: Navigate through 4-8 rally stages per event
5. **Results & Rewards**: Earn points, coins, and new cards
6. **Progression**: Unlock new rallies, upgrade cards, climb leaderboards

### Card Types & Mechanics

#### 1. Driver Cards (Primary Cards)
```
Stats:
- Speed: 1-100 (affects maximum stage times)
- Handling: 1-100 (reduces crash risk on difficult stages)
- Consistency: 1-100 (reduces time variation between stages)
- Adaptability: 1-100 (performs better in changing conditions)

Specializations:
- Tarmac Master: +20% performance on road stages
- Gravel Expert: +20% performance on dirt stages
- Weather Wizard: +15% performance in rain/snow
- Night Owl: +25% performance on night stages

Rarity Levels:
- Common: 60-75 base stats
- Rare: 70-85 base stats  
- Epic: 80-95 base stats
- Legendary: 90-100 base stats
```

#### 2. Co-Driver Cards (Support Cards)
```
Effects:
- Pace Notes Expert: -10% crash risk on all stages
- Route Master: +5% time bonus on complex stages
- Motivator: Driver gains +10 consistency during rally
- Technical Specialist: Vehicle reliability +15%

Synergy Bonuses:
- Same nationality as driver: +5% all stats
- Experience match: Veteran co-driver + veteran driver = +10% consistency
- Team chemistry: Some specific driver+co-driver pairs get unique bonuses
```

#### 3. Vehicle Cards (Equipment Cards)
```
Vehicle Classes:
- R1: Entry level, reliable but slow
- R2: Balanced performance
- R3: Good speed, moderate reliability
- R4: High performance, requires skilled drivers
- R5: Professional level, maximum performance
- WRC: Ultimate cars, legendary rarity only

Manufacturers (Fictional):
- Nordic Motors: High reliability, good in snow
- Alpine Racing: Excellent handling, mountain specialist
- Desert Works: Heat resistance, durability focus
- Thunder Dynamics: Maximum speed, lower reliability

Vehicle Stats:
- Top Speed: Maximum velocity on straights
- Acceleration: How quickly reaches top speed
- Handling: Cornering performance
- Reliability: Less likely to break down
- Weather Rating: Performance in adverse conditions
```

#### 4. Strategy Cards (Tactical Cards)
```
Setup Cards:
- Aggressive Setup: +10% speed, +15% crash risk
- Balanced Setup: No bonuses or penalties
- Conservative Setup: -5% speed, -20% crash risk

Tire Cards:
- Soft Tires: +15% speed, -25% durability
- Medium Tires: Balanced performance
- Hard Tires: -10% speed, +30% durability
- Weather Tires: +20% performance in rain/snow

Emergency Cards:
- Repair Kit: Fix mechanical damage during rally
- Spare Parts: Prevent one mechanical failure
- Weather Report: See upcoming stage conditions
- Pace Calculator: Optimal time targets for stages
```

#### 5. Stage Cards (Challenge Cards)
```
Stage Types:
- Sprint: Short, fast stages (2-5km)
- Technical: Lots of corners, handling focus
- High Speed: Long straights, top speed matters
- Mixed Surface: Combination of tarmac/gravel
- Extreme: Night, weather, or difficult conditions

Stage Hazards:
- Spectator Zones: High crash risk, but crowd bonus
- Jumps: Speed vs safety decision
- Narrow Sections: Precision driving required
- Weather Changes: Conditions change mid-stage
```

### Rally Competition Structure

#### Single Rally Event (15-20 minutes gameplay)
```
1. Event Selection:
   - Choose from 3-5 available rallies
   - Each has different stage types and rewards
   - Entry costs vary by difficulty/rewards

2. Team Preparation:
   - Select primary driver (1 required)
   - Choose co-driver (1 required)  
   - Pick vehicle for rally (1 required)
   - Add strategy cards (3-5 cards)
   - Set tire strategy for each stage

3. Stage Execution:
   - 4-8 stages per rally
   - Each stage presents:
     * Stage information (surface, length, conditions)
     * Risk vs reward decisions
     * Strategy card usage opportunities
     * Real-time tactical choices

4. Stage Resolution:
   - Calculate stage time based on:
     * Driver skill + Vehicle performance + Strategy cards
     * Random elements (weather, hazards)
     * Player decisions during stage
   - Risk/reward outcomes (push hard = faster but crash risk)
   - Damage accumulation affects later stages

5. Rally Results:
   - Final championship points awarded
   - Coins and XP earned
   - Chance for bonus card packs
   - Leaderboard position updates
```

#### Championship Progression
```
Bronze Championship (Stages 1-10):
- Unlock basic drivers and R1-R2 vehicles
- Learn core mechanics
- Reward: Access to Silver Championship

Silver Championship (Stages 11-25):
- More challenging stage combinations
- R3-R4 vehicles available
- Weather conditions introduced
- Reward: Access to Gold Championship  

Gold Championship (Stages 26-50):
- Professional difficulty
- R5 and WRC vehicles
- Night stages and extreme weather
- Legendary drivers available
- Reward: Elite Championship access

Elite Championship (Ongoing):
- Weekly rotating super-difficult events
- Exclusive legendary cards
- Global leaderboards
- Seasonal rewards and tournaments
```

---

## 🖥️ Your Own Server Setup

### Server Requirements

#### Minimum Specs (1,000-5,000 players)
```
Hardware:
- CPU: 2 cores, 2.4GHz (Intel i3 or AMD equivalent)
- RAM: 4GB DDR4
- Storage: 50GB SSD
- Bandwidth: 1TB/month
- OS: Ubuntu 20.04+ or CentOS 8+

Estimated Cost: $20-40/month (DigitalOcean, Vultr, Linode)
```

#### Recommended Specs (5,000-50,000 players)
```
Hardware:
- CPU: 4 cores, 3.0GHz+ (Intel i5/i7 or AMD Ryzen)
- RAM: 8GB DDR4
- Storage: 100GB+ SSD
- Bandwidth: 5TB/month
- OS: Ubuntu 22.04 LTS

Estimated Cost: $80-120/month
```

#### High-Performance Specs (50,000+ players)
```
Hardware:
- CPU: 8+ cores, 3.5GHz+
- RAM: 16GB+ DDR4
- Storage: 250GB+ NVMe SSD
- Bandwidth: 10TB+/month
- Load Balancer: Multiple server instances

Estimated Cost: $200-500/month + CDN costs
```

### Database Architecture

#### PostgreSQL Database Schema
```sql
-- Players and Authentication
CREATE TABLE players (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    coins INTEGER DEFAULT 1000,
    gems INTEGER DEFAULT 100,
    level INTEGER DEFAULT 1,
    xp INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Card Definitions (Master Data)
CREATE TABLE card_definitions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'driver', 'codriver', 'vehicle', 'strategy'
    rarity VARCHAR(20) NOT NULL, -- 'common', 'rare', 'epic', 'legendary'
    stats JSONB NOT NULL, -- {'speed': 85, 'handling': 92, 'consistency': 78}
    abilities JSONB, -- Special abilities and effects
    image_url VARCHAR(255),
    description TEXT,
    flavor_text TEXT,
    is_active BOOLEAN DEFAULT true
);

-- Player Card Collection
CREATE TABLE player_cards (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id) ON DELETE CASCADE,
    card_id INTEGER REFERENCES card_definitions(id),
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    obtained_at TIMESTAMP DEFAULT NOW(),
    is_favorite BOOLEAN DEFAULT false
);

-- Rally Events (Championships)
CREATE TABLE rally_events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    championship_tier VARCHAR(20) NOT NULL, -- 'bronze', 'silver', 'gold', 'elite'
    stages JSONB NOT NULL, -- Array of stage configurations
    entry_cost_coins INTEGER DEFAULT 0,
    entry_cost_gems INTEGER DEFAULT 0,
    rewards JSONB NOT NULL, -- Rewards structure
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP,
    end_date TIMESTAMP
);

-- Player Rally Results
CREATE TABLE rally_results (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    rally_id INTEGER REFERENCES rally_events(id),
    deck_used JSONB NOT NULL, -- Cards used in this rally
    stage_results JSONB NOT NULL, -- Individual stage times and results
    total_time INTEGER NOT NULL, -- Total rally time in milliseconds
    final_position INTEGER NOT NULL,
    championship_points INTEGER DEFAULT 0,
    rewards_earned JSONB,
    completed_at TIMESTAMP DEFAULT NOW()
);

-- Card Packs and Shop Items
CREATE TABLE shop_items (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'card_pack', 'currency', 'cosmetic'
    price_coins INTEGER DEFAULT 0,
    price_gems INTEGER DEFAULT 0,
    price_real_money DECIMAL(10,2) DEFAULT 0,
    contents JSONB NOT NULL, -- What's in the pack
    is_active BOOLEAN DEFAULT true,
    limited_quantity INTEGER, -- NULL for unlimited
    available_until TIMESTAMP
);

-- Purchase History
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    item_id INTEGER REFERENCES shop_items(id),
    payment_method VARCHAR(50), -- 'coins', 'gems', 'real_money'
    amount_paid DECIMAL(10,2),
    items_received JSONB,
    transaction_id VARCHAR(255), -- For real money purchases
    purchased_at TIMESTAMP DEFAULT NOW()
);

-- Global Leaderboards
CREATE TABLE leaderboards (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    championship_tier VARCHAR(20),
    total_points INTEGER DEFAULT 0,
    rallies_completed INTEGER DEFAULT 0,
    average_position DECIMAL(5,2),
    best_rally_time INTEGER,
    season VARCHAR(20), -- '2024_spring', etc.
    last_updated TIMESTAMP DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX idx_player_cards_player_id ON player_cards(player_id);
CREATE INDEX idx_rally_results_player_id ON rally_results(player_id);
CREATE INDEX idx_leaderboards_tier_points ON leaderboards(championship_tier, total_points DESC);
```

### Node.js Backend Structure

#### Project Directory
```
rally-card-api/
├── package.json
├── server.js                 # Main server file
├── .env                     # Environment variables
├── .gitignore
├── database/
│   ├── connection.js        # Database connection
│   ├── migrations/          # Database schema updates
│   └── seeds/               # Initial game data
├── models/
│   ├── Player.js           # Player data operations
│   ├── Card.js             # Card operations
│   ├── Rally.js            # Rally logic
│   └── Shop.js             # Shop operations
├── routes/
│   ├── auth.js             # Login, registration
│   ├── player.js           # Player profile, stats
│   ├── cards.js            # Card collection, upgrades
│   ├── rally.js            # Rally gameplay
│   └── shop.js             # In-app purchases
├── services/
│   ├── rallySimulator.js   # Rally calculation logic
│   ├── cardGenerator.js    # Random card generation
│   └── authService.js      # JWT token management
├── middleware/
│   ├── auth.js             # Authentication middleware
│   └── validation.js       # Input validation
└── utils/
    ├── constants.js        # Game constants
    └── helpers.js          # Utility functions
```

#### Key Environment Variables (.env)
```bash
# Server Configuration
PORT=3000
NODE_ENV=production

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=rally_game
DB_USER=rally_user
DB_PASSWORD=your_secure_password

# Security
JWT_SECRET=your_super_secure_jwt_secret_key_here
BCRYPT_ROUNDS=12

# External Services (Optional)
STRIPE_SECRET_KEY=sk_live_... # For real money purchases
SENDGRID_API_KEY=SG.... # For email notifications

# Game Configuration
STARTER_COINS=1000
STARTER_GEMS=100
DAILY_BONUS_COINS=50
```

---

## 🛠️ Development Setup & Requirements

### Development Environment Setup

#### 1. Install Required Software
```bash
# Node.js (v18+ recommended)
# Download from: https://nodejs.org/

# PostgreSQL Database
# Ubuntu/Debian:
sudo apt update
sudo apt install postgresql postgresql-contrib

# macOS (using Homebrew):
brew install postgresql
brew services start postgresql

# Windows:
# Download from: https://www.postgresql.org/download/windows/
```

#### 2. Database Setup
```sql
-- Connect to PostgreSQL as superuser
sudo -u postgres psql

-- Create database and user
CREATE DATABASE rally_game;
CREATE USER rally_user WITH ENCRYPTED PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE rally_game TO rally_user;

-- Exit and test connection
\q
psql -h localhost -U rally_user -d rally_game
```

#### 3. Initialize Node.js Project
```bash
# Create project directory
mkdir rally-card-api
cd rally-card-api

# Initialize package.json
npm init -y

# Install dependencies
npm install express cors helmet bcryptjs jsonwebtoken pg socket.io uuid joi dotenv multer

# Install development dependencies
npm install --save-dev nodemon

# Create basic structure
mkdir database models routes services middleware utils
touch server.js .env .gitignore
```

#### 4. Flutter Development Setup
```bash
# Install Flutter SDK
# Download from: https://flutter.dev/docs/get-started/install

# Verify installation
flutter doctor

# Create Flutter project
flutter create rally_manager
cd rally_manager

# Add required dependencies to pubspec.yaml
flutter pub add http provider shared_preferences
flutter pub add socket_io_client uuid
```

### Development Timeline & Milestones

#### Phase 1: Backend Foundation (Weeks 1-3)
```
Week 1:
- Set up Node.js server with basic routing
- Database schema creation and migrations
- User authentication (register/login)
- Basic player profile management

Week 2:
- Card system implementation
- Card collection management
- Basic shop functionality (buy packs with coins)
- Rally event data structure

Week 3:
- Rally simulation logic
- Scoring and results calculation
- Leaderboard system
- API testing and documentation
```

#### Phase 2: Flutter App Foundation (Weeks 2-5)
```
Week 2-3 (Parallel with backend):
- Flutter project setup and navigation
- Login/register screens
- Basic UI components and theme

Week 4:
- Card collection display
- Deck builder interface
- Shop screen for purchasing packs

Week 5:
- Rally selection screen
- Basic gameplay interface
- Results and progression screens
```

#### Phase 3: Game Logic Integration (Weeks 4-6)
```
Week 4-5:
- Connect Flutter app to Node.js API
- Implement rally gameplay flow
- Real-time updates during rallies
- Card upgrade and progression systems

Week 6:
- Multiplayer rally features (WebSocket)
- Push notifications for events
- Performance optimization and bug fixes
```

#### Phase 4: Art and Polish (Weeks 6-10)
```
Week 6-8:
- Commission or create card artwork
- UI/UX improvements and animations
- Sound effects and music integration
- Loading screens and transitions

Week 9-10:
- Extensive testing on multiple devices
- Performance optimization
- Store listing preparation
- Marketing material creation
```

#### Phase 5: Launch Preparation (Weeks 10-12)
```
Week 10-11:
- Beta testing with friends/family
- Bug fixes and balance adjustments
- Server deployment and scaling preparation
- Analytics and monitoring setup

Week 12:
- Google Play Store submission
- Marketing campaign launch
- Community building (Discord, social media)
- Post-launch support preparation
```

---

## 💰 Monetization Strategy

### Revenue Streams

#### 1. Card Packs (Primary Revenue)
```
Bronze Pack: $0.99
- 3 cards guaranteed
- 70% common, 25% rare, 5% epic

Silver Pack: $2.99
- 5 cards guaranteed  
- 50% common, 35% rare, 12% epic, 3% legendary

Gold Pack: $4.99
- 8 cards guaranteed
- 30% common, 40% rare, 25% epic, 5% legendary

Legendary Pack: $9.99
- 5 cards guaranteed
- At least 1 legendary, rest rare or better
```

#### 2. Season Pass
```
Premium Season Pass: $9.99 (3 months)
- Exclusive legendary cards
- Double XP and coin rewards
- Special cosmetics and effects
- Early access to new content
- Premium rally events
```

#### 3. Convenience Items
```
Coin Packages:
- Small: 1,000 coins for $0.99
- Medium: 5,000 coins for $3.99
- Large: 15,000 coins for $9.99

Gem Packages:
- Small: 100 gems for $0.99
- Medium: 500 gems for $3.99
- Large: 1,500 gems for $9.99
```

#### 4. Advertisement Revenue
```
Rewarded Videos:
- Watch ad for 50 bonus coins (3x daily)
- Double rally rewards after completing event
- Free card pack every 4 hours with ad

Banner Ads:
- Non-intrusive banners in menus
- $1-3 CPM depending on region and demographics
```

### Expected Revenue Projections
```
Conservative Scenario (5,000 monthly active users):
- 2% conversion rate = 100 paying users
- $5 average monthly spend per paying user
- Monthly Revenue: $500
- Annual Revenue: $6,000

Realistic Scenario (25,000 monthly active users):
- 5% conversion rate = 1,250 paying users
- $8 average monthly spend per paying user  
- Monthly Revenue: $10,000
- Annual Revenue: $120,000

Optimistic Scenario (100,000 monthly active users):
- 8% conversion rate = 8,000 paying users
- $12 average monthly spend per paying user
- Monthly Revenue: $96,000
- Annual Revenue: $1,150,000
```

---

## 🚀 Launch Strategy

### Pre-Launch (4 weeks before)
- Build anticipation on social media
- Create teaser trailer showcasing gameplay
- Set up landing page with email signup
- Reach out to gaming influencers
- Submit to app stores for review

### Launch Week
- Coordinate social media campaign
- Press release to gaming blogs
- Influencer sponsored content
- Community events and giveaways
- Monitor server performance and user feedback

### Post-Launch (First Month)
- Daily player data analysis
- Rapid bug fixes and improvements
- Regular content updates (new cards, events)
- Community management and feedback incorporation
- Plan first major content expansion

---

## 📊 Success Metrics & KPIs

### Key Performance Indicators
```
User Acquisition:
- Daily/Monthly Active Users (DAU/MAU)
- User acquisition cost (UAC)
- Organic vs paid download ratio

Engagement:
- Session length (target: 15+ minutes)
- Sessions per day (target: 3+)
- Day 1/7/30 retention rates (target: 40%/20%/10%)

Monetization:
- Average Revenue Per User (ARPU)
- Lifetime Value (LTV)
- Conversion rate (free to paying)
- Monthly recurring revenue (MRR)

Technical:
- App store rating (target: 4.0+)
- Crash rate (target: <2%)
- Server uptime (target: 99.5%+)
- API response times (target: <200ms)
```

### Success Benchmarks
```
3 Months Post-Launch:
- 10,000+ total downloads
- 2,000+ monthly active users  
- $2,000+ monthly revenue
- 4.0+ app store rating

6 Months Post-Launch:
- 50,000+ total downloads
- 8,000+ monthly active users
- $8,000+ monthly revenue
- Positive user acquisition cost vs LTV

12 Months Post-Launch:
- 200,000+ total downloads
- 25,000+ monthly active users
- $25,000+ monthly revenue
- Sustainable growth and profitability
```

This comprehensive guide covers everything you need to create, develop, host, and launch your Rally Card Game successfully!