import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/deck_provider.dart';
import '../../providers/card_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/card_widget.dart';
import '../../models/card.dart';
import 'rally_simulation_screen.dart';

class DeckSelectionScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> rallyInfo;

  const DeckSelectionScreen({
    super.key,
    required this.rallyInfo,
  });

  @override
  ConsumerState<DeckSelectionScreen> createState() => _DeckSelectionScreenState();
}

class _DeckSelectionScreenState extends ConsumerState<DeckSelectionScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  // Mock cards for demonstration
  final List<GameCard> _mockCards = [
    const GameCard(
      id: 1,
      name: 'Speed Racer',
      type: 'driver',
      rarity: 'epic',
      stats: {'speed': 95, 'handling': 75, 'acceleration': 85},
      description: 'Master of high-speed racing',
    ),
    const GameCard(
      id: 2,
      name: 'Navigator Pro',
      type: 'codriver',
      rarity: 'rare',
      stats: {'navigation': 90, 'communication': 85, 'experience': 80},
      description: 'Expert rally navigator',
    ),
    const GameCard(
      id: 3,
      name: 'Rally Beast',
      type: 'vehicle',
      rarity: 'legendary',
      stats: {'speed': 90, 'handling': 95, 'durability': 85},
      description: 'Ultimate rally machine',
    ),
    const GameCard(
      id: 4,
      name: 'Turbo Boost',
      type: 'strategy',
      rarity: 'epic',
      stats: {'boost_power': 20, 'duration': 15},
      description: 'Temporary speed increase',
    ),
    const GameCard(
      id: 5,
      name: 'Perfect Line',
      type: 'strategy',
      rarity: 'rare',
      stats: {'handling_bonus': 15, 'corner_speed': 10},
      description: 'Optimal racing line assistance',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    
    // Load deck data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(deckProvider.notifier).loadDecks();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final deckState = ref.watch(deckProvider);
    final currentDeck = ref.watch(currentDeckProvider);
    final deckValidation = ref.watch(deckValidationProvider);

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: AppBar(
        backgroundColor: AppTheme.darkSurface,
        title: Text(
          'Select Your Deck',
          style: AppTheme.headingMedium.copyWith(color: Colors.white),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Column(
        children: [
          // Rally info header
          _buildRallyHeader(),
          
          // Current deck overview
          _buildCurrentDeckOverview(currentDeck, deckValidation),
          
          // Deck building tabs
          _buildDeckBuildingTabs(),
          
          // Card selection area
          Expanded(
            child: _buildCardSelectionArea(),
          ),
          
          // Action buttons
          _buildActionButtons(deckValidation),
        ],
      ),
    );
  }

  Widget _buildRallyHeader() {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: AppTheme.darkCard,
        border: Border(
          bottom: BorderSide(color: Colors.white12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.emoji_events,
            color: AppTheme.getTierColor(widget.rallyInfo['tier'] ?? 'bronze'),
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.rallyInfo['name'] ?? 'Rally Event',
                  style: AppTheme.headingSmall.copyWith(color: Colors.white),
                ),
                Text(
                  '${widget.rallyInfo['stages'] ?? 5} stages • ${widget.rallyInfo['difficulty'] ?? 'Medium'}',
                  style: AppTheme.bodySmall.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.monetization_on, color: AppTheme.warningColor, size: 16),
                  const SizedBox(width: 4),
                  Text(
                    '${widget.rallyInfo['coins'] ?? 100}',
                    style: AppTheme.bodySmall.copyWith(color: AppTheme.warningColor),
                  ),
                ],
              ),
              if ((widget.rallyInfo['gems'] ?? 0) > 0) ...[
                const SizedBox(height: 2),
                Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(Icons.diamond, color: AppTheme.primaryColor, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${widget.rallyInfo['gems']}',
                      style: AppTheme.bodySmall.copyWith(color: AppTheme.primaryColor),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentDeckOverview(Deck? deck, DeckValidation validation) {
    if (deck == null) {
      return Container(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Text(
          'No deck selected',
          style: AppTheme.bodyMedium.copyWith(color: Colors.white54),
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: AppTheme.darkCard,
        border: Border(bottom: BorderSide(color: Colors.white12)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  deck.name,
                  style: AppTheme.headingSmall.copyWith(color: Colors.white),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: validation.isValid ? AppTheme.successColor : AppTheme.errorColor,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  validation.isValid ? 'READY' : 'INCOMPLETE',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              _buildDeckSlot('Driver', _getCardById(deck.driverCardId)),
              const SizedBox(width: 8),
              _buildDeckSlot('Co-driver', _getCardById(deck.codriverCardId)),
              const SizedBox(width: 8),
              _buildDeckSlot('Vehicle', _getCardById(deck.vehicleCardId)),
              const SizedBox(width: 8),
              _buildStrategySlots(deck.strategyCards),
            ],
          ),
          if (validation.errors.isNotEmpty || validation.warnings.isNotEmpty) ...[
            const SizedBox(height: 8),
            ...validation.errors.map((error) => _buildValidationMessage(error, true)),
            ...validation.warnings.map((warning) => _buildValidationMessage(warning, false)),
          ],
        ],
      ),
    );
  }

  Widget _buildDeckSlot(String label, GameCard? card) {
    return Expanded(
      child: Column(
        children: [
          Container(
            height: 60,
            decoration: BoxDecoration(
              color: card != null 
                ? AppTheme.getRarityColor(card.rarity).withOpacity(0.2)
                : Colors.white12,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: card != null 
                  ? AppTheme.getRarityColor(card.rarity)
                  : Colors.white24,
              ),
            ),
            child: Center(
              child: card != null
                ? Icon(
                    _getCardTypeIcon(card.type),
                    color: AppTheme.getRarityColor(card.rarity),
                    size: 24,
                  )
                : const Icon(
                    Icons.add,
                    color: Colors.white38,
                    size: 24,
                  ),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStrategySlots(List<int> strategyCardIds) {
    return Expanded(
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 28,
                  decoration: BoxDecoration(
                    color: strategyCardIds.isNotEmpty
                      ? AppTheme.getRarityColor(_getCardById(strategyCardIds[0])?.rarity ?? 'common').withOpacity(0.2)
                      : Colors.white12,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: strategyCardIds.isNotEmpty
                        ? AppTheme.getRarityColor(_getCardById(strategyCardIds[0])?.rarity ?? 'common')
                        : Colors.white24,
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      strategyCardIds.isNotEmpty ? Icons.psychology : Icons.add,
                      color: strategyCardIds.isNotEmpty
                        ? AppTheme.getRarityColor(_getCardById(strategyCardIds[0])?.rarity ?? 'common')
                        : Colors.white38,
                      size: 16,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 4),
              Expanded(
                child: Container(
                  height: 28,
                  decoration: BoxDecoration(
                    color: strategyCardIds.length > 1
                      ? AppTheme.getRarityColor(_getCardById(strategyCardIds[1])?.rarity ?? 'common').withOpacity(0.2)
                      : Colors.white12,
                    borderRadius: BorderRadius.circular(4),
                    border: Border.all(
                      color: strategyCardIds.length > 1
                        ? AppTheme.getRarityColor(_getCardById(strategyCardIds[1])?.rarity ?? 'common')
                        : Colors.white24,
                    ),
                  ),
                  child: Center(
                    child: Icon(
                      strategyCardIds.length > 1 ? Icons.psychology : Icons.add,
                      color: strategyCardIds.length > 1
                        ? AppTheme.getRarityColor(_getCardById(strategyCardIds[1])?.rarity ?? 'common')
                        : Colors.white38,
                      size: 16,
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            'Strategy',
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white70,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildValidationMessage(String message, bool isError) {
    return Padding(
      padding: const EdgeInsets.only(top: 4),
      child: Row(
        children: [
          Icon(
            isError ? Icons.error_outline : Icons.warning_outline,
            color: isError ? AppTheme.errorColor : AppTheme.warningColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              message,
              style: AppTheme.bodySmall.copyWith(
                color: isError ? AppTheme.errorColor : AppTheme.warningColor,
                fontSize: 11,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeckBuildingTabs() {
    return Container(
      color: AppTheme.darkSurface,
      child: TabBar(
        controller: _tabController,
        indicatorColor: AppTheme.primaryColor,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.white54,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTheme.bodySmall,
        tabs: const [
          Tab(text: 'DRIVERS'),
          Tab(text: 'CO-DRIVERS'),
          Tab(text: 'VEHICLES'),
          Tab(text: 'STRATEGY'),
        ],
      ),
    );
  }

  Widget _buildCardSelectionArea() {
    return TabBarView(
      controller: _tabController,
      children: [
        _buildCardGrid('driver'),
        _buildCardGrid('codriver'),
        _buildCardGrid('vehicle'),
        _buildCardGrid('strategy'),
      ],
    );
  }

  Widget _buildCardGrid(String cardType) {
    final cards = _mockCards.where((card) => card.type == cardType).toList();
    
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: AppConstants.cardAspectRatio,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
        ),
        itemCount: cards.length,
        itemBuilder: (context, index) {
          final card = cards[index];
          final isSelected = _isCardSelected(card);
          
          return CardWidget(
            gameCard: card,
            showLevel: false,
            showFavorite: false,
            isSelected: isSelected,
            onTap: () => _selectCard(card),
          ).animate().fadeIn(
            delay: (index * 100).ms,
            duration: 300.ms,
          );
        },
      ),
    );
  }

  Widget _buildActionButtons(DeckValidation validation) {
    return Container(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      decoration: const BoxDecoration(
        color: AppTheme.darkSurface,
        border: Border(top: BorderSide(color: Colors.white12)),
      ),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: PrimaryButton(
              text: 'Start Rally',
              onPressed: validation.isValid ? _startRally : null,
              icon: Icons.flag,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: SecondaryButton(
              text: 'Save Deck',
              onPressed: _saveDeck,
              icon: Icons.save,
            ),
          ),
        ],
      ),
    );
  }

  GameCard? _getCardById(int cardId) {
    try {
      return _mockCards.firstWhere((card) => card.id == cardId);
    } catch (e) {
      return null;
    }
  }

  bool _isCardSelected(GameCard card) {
    final currentDeck = ref.read(currentDeckProvider);
    if (currentDeck == null) return false;

    switch (card.type) {
      case 'driver':
        return currentDeck.driverCardId == card.id;
      case 'codriver':
        return currentDeck.codriverCardId == card.id;
      case 'vehicle':
        return currentDeck.vehicleCardId == card.id;
      case 'strategy':
        return currentDeck.strategyCards.contains(card.id);
      default:
        return false;
    }
  }

  void _selectCard(GameCard card) {
    ref.read(deckProvider.notifier).updateDeckCard(card.type, card.id);
  }

  void _saveDeck() async {
    final currentDeck = ref.read(currentDeckProvider);
    if (currentDeck != null) {
      await ref.read(deckProvider.notifier).saveDeck(currentDeck);
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Deck saved successfully!'),
            backgroundColor: AppTheme.successColor,
          ),
        );
      }
    }
  }

  void _startRally() {
    final currentDeck = ref.read(currentDeckProvider);
    if (currentDeck != null) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => RallySimulationScreen(
            rallyInfo: widget.rallyInfo,
            deck: currentDeck,
          ),
        ),
      );
    }
  }

  IconData _getCardTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'driver':
        return Icons.person;
      case 'codriver':
        return Icons.people;
      case 'vehicle':
        return Icons.directions_car;
      case 'strategy':
        return Icons.psychology;
      default:
        return Icons.help_outline;
    }
  }
}
