// Game constants and configuration

// Card rarities and their probabilities
const CARD_RARITIES = {
  COMMON: 'common',
  RARE: 'rare',
  EPIC: 'epic',
  LEGENDARY: 'legendary'
};

// Pack probabilities (percentages)
const PACK_PROBABILITIES = {
  BRONZE: {
    [CARD_RARITIES.COMMON]: 70,
    [CARD_RARITIES.RARE]: 25,
    [CARD_RARITIES.EPIC]: 5,
    [CARD_RARITIES.LEGENDARY]: 0
  },
  SILVER: {
    [CARD_RARITIES.COMMON]: 50,
    [CARD_RARITIES.RARE]: 35,
    [CARD_RARITIES.EPIC]: 12,
    [CARD_RARITIES.LEGENDARY]: 3
  },
  GOLD: {
    [CARD_RARITIES.COMMON]: 30,
    [CARD_RARITIES.RARE]: 40,
    [CARD_RARITIES.EPIC]: 25,
    [CARD_RARITIES.LEGENDARY]: 5
  },
  LEGENDARY: {
    [CARD_RARITIES.COMMON]: 0,
    [CARD_RARITIES.RARE]: 40,
    [CARD_RARITIES.EPIC]: 40,
    [CARD_RARITIES.LEGENDARY]: 20
  }
};

// Card types
const CARD_TYPES = {
  DRIVER: 'driver',
  CODRIVER: 'codriver',
  VEHICLE: 'vehicle',
  STRATEGY: 'strategy'
};

// Championship tiers
const CHAMPIONSHIP_TIERS = {
  BRONZE: 'bronze',
  SILVER: 'silver',
  GOLD: 'gold',
  ELITE: 'elite'
};

// Stage types
const STAGE_TYPES = {
  SPRINT: 'sprint',
  TECHNICAL: 'technical',
  HIGH_SPEED: 'high_speed',
  MIXED_SURFACE: 'mixed_surface',
  EXTREME: 'extreme'
};

// Weather conditions
const WEATHER_CONDITIONS = {
  CLEAR: 'clear',
  RAIN: 'rain',
  SNOW: 'snow',
  FOG: 'fog'
};

// Risk levels for stage decisions
const RISK_LEVELS = {
  CONSERVATIVE: 'conservative',
  BALANCED: 'balanced',
  AGGRESSIVE: 'aggressive'
};

// Vehicle classes
const VEHICLE_CLASSES = {
  R1: 'r1',
  R2: 'r2',
  R3: 'r3',
  R4: 'r4',
  R5: 'r5',
  WRC: 'wrc'
};

// Experience points and leveling
const XP_REQUIREMENTS = {
  1: 0,
  2: 100,
  3: 250,
  4: 450,
  5: 700,
  6: 1000,
  7: 1350,
  8: 1750,
  9: 2200,
  10: 2700,
  // Continue pattern: each level requires 50 more XP than the previous increment
};

// Rally rewards based on position
const RALLY_REWARDS = {
  CHAMPIONSHIP_POINTS: {
    1: 25,
    2: 20,
    3: 16,
    4: 13,
    5: 11,
    6: 10,
    7: 9,
    8: 8,
    9: 7,
    10: 6
  },
  BASE_COINS: {
    1: 200,
    2: 150,
    3: 120,
    4: 100,
    5: 90,
    6: 80,
    7: 70,
    8: 60,
    9: 50,
    10: 40
  }
};

// Shop item types
const SHOP_ITEM_TYPES = {
  CARD_PACK: 'card_pack',
  CURRENCY: 'currency',
  COSMETIC: 'cosmetic'
};

// Currency types
const CURRENCIES = {
  COINS: 'coins',
  GEMS: 'gems',
  REAL_MONEY: 'real_money'
};

// Game limits
const GAME_LIMITS = {
  MAX_DECK_SIZE: 20,
  MIN_DECK_SIZE: 15,
  MIN_STRATEGY_CARDS: 3,
  MAX_STRATEGY_CARDS: 5,
  MAX_DAILY_RALLIES: 10,
  MAX_USERNAME_LENGTH: 20,
  MIN_USERNAME_LENGTH: 3
};

// Error messages
const ERROR_MESSAGES = {
  INVALID_CREDENTIALS: 'Invalid email or password',
  USER_EXISTS: 'User with this email or username already exists',
  INSUFFICIENT_FUNDS: 'Insufficient coins or gems',
  INVALID_DECK: 'Invalid deck configuration',
  RALLY_NOT_FOUND: 'Rally event not found',
  CARD_NOT_FOUND: 'Card not found',
  UNAUTHORIZED: 'Unauthorized access',
  SERVER_ERROR: 'Internal server error'
};

module.exports = {
  CARD_RARITIES,
  PACK_PROBABILITIES,
  CARD_TYPES,
  CHAMPIONSHIP_TIERS,
  STAGE_TYPES,
  WEATHER_CONDITIONS,
  RISK_LEVELS,
  VEHICLE_CLASSES,
  XP_REQUIREMENTS,
  RALLY_REWARDS,
  SHOP_ITEM_TYPES,
  CURRENCIES,
  GAME_LIMITS,
  ERROR_MESSAGES
};
