import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';

import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../models/card.dart';
import '../models/shop.dart';
import '../widgets/card_widget.dart';

class PackOpeningAnimation extends StatefulWidget {
  final PackOpeningResult packResult;
  final VoidCallback? onComplete;
  final VoidCallback? onSkip;

  const PackOpeningAnimation({
    super.key,
    required this.packResult,
    this.onComplete,
    this.onSkip,
  });

  @override
  State<PackOpeningAnimation> createState() => _PackOpeningAnimationState();
}

class _PackOpeningAnimationState extends State<PackOpeningAnimation>
    with TickerProviderStateMixin {
  late AnimationController _packController;
  late AnimationController _cardsController;
  late AnimationController _resultsController;
  late ConfettiController _confettiController;

  late Animation<double> _packScaleAnimation;
  late Animation<double> _packRotationAnimation;
  late Animation<double> _packOpacityAnimation;
  late Animation<double> _glowAnimation;

  int _currentPhase = 0; // 0: pack, 1: cards reveal, 2: results
  int _revealedCards = 0;
  bool _canSkip = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startPackAnimation();
  }

  void _initializeAnimations() {
    _packController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _cardsController = AnimationController(
      duration: const Duration(milliseconds: 3000),
      vsync: this,
    );

    _resultsController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _packScaleAnimation = Tween<double>(
      begin: 0.5,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _packController,
      curve: const Interval(0.0, 0.6, curve: Curves.elasticOut),
    ));

    _packRotationAnimation = Tween<double>(
      begin: 0.0,
      end: 0.1,
    ).animate(CurvedAnimation(
      parent: _packController,
      curve: const Interval(0.4, 0.8, curve: Curves.easeInOut),
    ));

    _packOpacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _packController,
      curve: const Interval(0.8, 1.0, curve: Curves.easeIn),
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _packController,
      curve: const Interval(0.3, 0.7, curve: Curves.easeInOut),
    ));
  }

  void _startPackAnimation() async {
    setState(() {
      _canSkip = true;
    });

    await _packController.forward();
    
    if (mounted) {
      setState(() {
        _currentPhase = 1;
      });
      _startCardsReveal();
    }
  }

  void _startCardsReveal() async {
    // Start confetti if there's a rare+ card
    if (widget.packResult.hasRareOrBetter) {
      _confettiController.play();
    }

    // Reveal cards one by one
    for (int i = 0; i < widget.packResult.cards.length; i++) {
      if (!mounted) return;
      
      setState(() {
        _revealedCards = i + 1;
      });
      
      await Future.delayed(const Duration(milliseconds: 600));
    }

    if (mounted) {
      await Future.delayed(const Duration(milliseconds: 500));
      setState(() {
        _currentPhase = 2;
      });
      _resultsController.forward();
      
      // Auto-complete after showing results
      Future.delayed(const Duration(milliseconds: 2000), () {
        if (mounted) {
          widget.onComplete?.call();
        }
      });
    }
  }

  void _skipAnimation() {
    if (!_canSkip) return;
    
    setState(() {
      _currentPhase = 2;
      _revealedCards = widget.packResult.cards.length;
    });
    
    _packController.stop();
    _cardsController.stop();
    _resultsController.forward();
    
    widget.onSkip?.call();
  }

  @override
  void dispose() {
    _packController.dispose();
    _cardsController.dispose();
    _resultsController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // Background gradient
          Container(
            decoration: BoxDecoration(
              gradient: RadialGradient(
                center: Alignment.center,
                radius: 1.0,
                colors: [
                  AppTheme.getRarityColor(widget.packResult.highestRarity).withOpacity(0.3),
                  Colors.black,
                  Colors.black,
                ],
              ),
            ),
          ),

          // Main content
          SafeArea(
            child: Column(
              children: [
                // Header
                _buildHeader(),
                
                // Main animation area
                Expanded(
                  child: _buildMainContent(),
                ),
                
                // Skip button
                if (_canSkip && _currentPhase < 2)
                  _buildSkipButton(),
              ],
            ),
          ),

          // Confetti
          Align(
            alignment: Alignment.topCenter,
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              shouldLoop: false,
              colors: const [
                AppTheme.primaryColor,
                AppTheme.accentColor,
                AppTheme.successColor,
                AppTheme.warningColor,
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: Text(
              '${widget.packResult.packType.toUpperCase()} PACK',
              style: AppTheme.headingMedium.copyWith(
                color: AppTheme.getRarityColor(widget.packResult.packType),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMainContent() {
    switch (_currentPhase) {
      case 0:
        return _buildPackAnimation();
      case 1:
        return _buildCardsReveal();
      case 2:
        return _buildResults();
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildPackAnimation() {
    return Center(
      child: AnimatedBuilder(
        animation: _packController,
        builder: (context, child) {
          return Transform.scale(
            scale: _packScaleAnimation.value,
            child: Transform.rotate(
              angle: _packRotationAnimation.value,
              child: Opacity(
                opacity: _packOpacityAnimation.value,
                child: Container(
                  width: 200,
                  height: 280,
                  decoration: BoxDecoration(
                    color: AppTheme.getRarityColor(widget.packResult.packType).withOpacity(0.3),
                    borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                    border: Border.all(
                      color: AppTheme.getRarityColor(widget.packResult.packType),
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: AppTheme.getRarityColor(widget.packResult.packType).withOpacity(0.5 + (_glowAnimation.value * 0.5)),
                        blurRadius: 20 + (_glowAnimation.value * 20),
                        spreadRadius: 5 + (_glowAnimation.value * 10),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.card_giftcard,
                        size: 80,
                        color: AppTheme.getRarityColor(widget.packResult.packType),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        widget.packResult.packType.toUpperCase(),
                        style: AppTheme.headingSmall.copyWith(
                          color: AppTheme.getRarityColor(widget.packResult.packType),
                        ),
                      ),
                      Text(
                        'PACK',
                        style: AppTheme.bodyMedium.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCardsReveal() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        children: [
          Text(
            'Your Cards',
            style: AppTheme.headingMedium.copyWith(
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 24),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: AppConstants.cardAspectRatio,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: widget.packResult.cards.length,
              itemBuilder: (context, index) {
                final card = widget.packResult.cards[index];
                final isRevealed = index < _revealedCards;
                
                return AnimatedSwitcher(
                  duration: const Duration(milliseconds: 600),
                  child: isRevealed
                    ? CardWidget(
                        key: ValueKey('card_$index'),
                        gameCard: card,
                        showLevel: false,
                        showFavorite: false,
                      ).animate().scale(
                        begin: const Offset(0.5, 0.5),
                        end: const Offset(1.0, 1.0),
                        duration: 400.ms,
                        curve: Curves.elasticOut,
                      ).fadeIn(duration: 200.ms)
                    : Container(
                        key: ValueKey('placeholder_$index'),
                        decoration: BoxDecoration(
                          color: Colors.white12,
                          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                          border: Border.all(
                            color: Colors.white24,
                          ),
                        ),
                        child: const Center(
                          child: Icon(
                            Icons.help_outline,
                            color: Colors.white38,
                            size: 32,
                          ),
                        ),
                      ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResults() {
    return AnimatedBuilder(
      animation: _resultsController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _resultsController,
          child: SlideTransition(
            position: Tween<Offset>(
              begin: const Offset(0, 0.3),
              end: Offset.zero,
            ).animate(_resultsController),
            child: Padding(
              padding: const EdgeInsets.all(AppConstants.defaultPadding),
              child: Column(
                children: [
                  Text(
                    'Pack Opened!',
                    style: AppTheme.headingLarge.copyWith(
                      color: AppTheme.getRarityColor(widget.packResult.highestRarity),
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildPackSummary(),
                  const SizedBox(height: 24),
                  _buildPackStats(),
                  const Spacer(),
                  _buildContinueButton(),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildPackSummary() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppTheme.getRarityColor(widget.packResult.highestRarity).withOpacity(0.3),
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildStatItem('Cards', widget.packResult.cards.length.toString()),
              _buildStatItem('Value', '${widget.packResult.packValue} coins'),
              _buildStatItem('Best', widget.packResult.highestRarity.toUpperCase()),
            ],
          ),
          if (widget.packResult.hasLegendary) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.getRarityColor('legendary').withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '✨ LEGENDARY CARD! ✨',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.getRarityColor('legendary'),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildPackStats() {
    final stats = widget.packResult.packStats;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Pack Breakdown',
            style: AppTheme.headingSmall.copyWith(
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              if (stats.common > 0)
                _buildRarityCount('Common', stats.common, 'common'),
              if (stats.rare > 0)
                _buildRarityCount('Rare', stats.rare, 'rare'),
              if (stats.epic > 0)
                _buildRarityCount('Epic', stats.epic, 'epic'),
              if (stats.legendary > 0)
                _buildRarityCount('Legendary', stats.legendary, 'legendary'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTheme.headingSmall.copyWith(
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildRarityCount(String rarity, int count, String rarityKey) {
    return Column(
      children: [
        Text(
          count.toString(),
          style: AppTheme.bodyMedium.copyWith(
            color: AppTheme.getRarityColor(rarityKey),
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          rarity,
          style: AppTheme.bodySmall.copyWith(
            color: AppTheme.getRarityColor(rarityKey),
          ),
        ),
      ],
    );
  }

  Widget _buildSkipButton() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: TextButton(
        onPressed: _skipAnimation,
        child: Text(
          'Skip Animation',
          style: AppTheme.bodyMedium.copyWith(
            color: Colors.white70,
          ),
        ),
      ),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: () {
          widget.onComplete?.call();
          Navigator.of(context).pop();
        },
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.getRarityColor(widget.packResult.highestRarity),
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
        child: Text(
          'Continue',
          style: AppTheme.labelLarge.copyWith(
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}
