import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/shop.dart';
import '../services/api_service.dart';

// Shop items provider
final shopItemsProvider = StateNotifierProvider<ShopNotifier, ShopState>((ref) {
  return ShopNotifier();
});

// Purchase history provider
final purchaseHistoryProvider = StateNotifierProvider<PurchaseHistoryNotifier, PurchaseHistoryState>((ref) {
  return PurchaseHistoryNotifier();
});

// Daily bonus provider
final dailyBonusProvider = StateNotifierProvider<DailyBonusNotifier, DailyBonusState>((ref) {
  return DailyBonusNotifier();
});

// Filtered shop items provider
final filteredShopItemsProvider = Provider<List<ShopItem>>((ref) {
  final shopState = ref.watch(shopItemsProvider);
  final filter = ref.watch(shopFilterProvider);
  
  if (shopState.items.isEmpty) return [];
  
  var filteredItems = shopState.items.where((item) {
    if (filter.type != null && item.type != filter.type) return false;
    if (filter.showOnlyAvailable && !item.isAvailable) return false;
    if (filter.showOnlyDiscounted && item.discountPercentage == null) return false;
    return true;
  }).toList();
  
  // Sort items
  switch (filter.sortBy) {
    case ShopSortBy.name:
      filteredItems.sort((a, b) => a.name.compareTo(b.name));
      break;
    case ShopSortBy.priceCoinsLow:
      filteredItems.sort((a, b) => a.discountedCoinsPrice.compareTo(b.discountedCoinsPrice));
      break;
    case ShopSortBy.priceCoinsHigh:
      filteredItems.sort((a, b) => b.discountedCoinsPrice.compareTo(a.discountedCoinsPrice));
      break;
    case ShopSortBy.priceGemsLow:
      filteredItems.sort((a, b) => a.discountedGemsPrice.compareTo(b.discountedGemsPrice));
      break;
    case ShopSortBy.priceGemsHigh:
      filteredItems.sort((a, b) => b.discountedGemsPrice.compareTo(a.discountedGemsPrice));
      break;
  }
  
  return filteredItems;
});

// Shop filter provider
final shopFilterProvider = StateNotifierProvider<ShopFilterNotifier, ShopFilter>((ref) {
  return ShopFilterNotifier();
});

// Shop State
class ShopState {
  final List<ShopItem> items;
  final bool isLoading;
  final String? error;

  const ShopState({
    this.items = const [],
    this.isLoading = false,
    this.error,
  });

  ShopState copyWith({
    List<ShopItem>? items,
    bool? isLoading,
    String? error,
  }) {
    return ShopState(
      items: items ?? this.items,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Shop Notifier
class ShopNotifier extends StateNotifier<ShopState> {
  ShopNotifier() : super(const ShopState());

  Future<void> loadShopItems() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await ApiService.instance.getShopItems();
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          items: response.data!,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load shop items',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load shop items: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  Future<PurchaseResult?> purchaseItem(int itemId, String paymentMethod) async {
    try {
      final response = await ApiService.instance.purchaseItem(itemId, paymentMethod);
      
      if (response.isSuccess && response.data != null) {
        return response.data!;
      } else {
        state = state.copyWith(error: response.error ?? 'Purchase failed');
        return null;
      }
    } catch (e) {
      state = state.copyWith(error: 'Purchase failed: ${e.toString()}');
      return null;
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Purchase History State
class PurchaseHistoryState {
  final List<Purchase> purchases;
  final bool isLoading;
  final String? error;

  const PurchaseHistoryState({
    this.purchases = const [],
    this.isLoading = false,
    this.error,
  });

  PurchaseHistoryState copyWith({
    List<Purchase>? purchases,
    bool? isLoading,
    String? error,
  }) {
    return PurchaseHistoryState(
      purchases: purchases ?? this.purchases,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Purchase History Notifier
class PurchaseHistoryNotifier extends StateNotifier<PurchaseHistoryState> {
  PurchaseHistoryNotifier() : super(const PurchaseHistoryState());

  Future<void> loadPurchaseHistory() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // TODO: Implement API call for purchase history
      // For now, return empty list
      state = state.copyWith(
        purchases: [],
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load purchase history: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  void addPurchase(Purchase purchase) {
    final updatedPurchases = [purchase, ...state.purchases];
    state = state.copyWith(purchases: updatedPurchases);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Daily Bonus State
class DailyBonusState {
  final DailyBonus? bonus;
  final bool isLoading;
  final String? error;
  final bool canClaim;
  final DateTime? lastClaimTime;

  const DailyBonusState({
    this.bonus,
    this.isLoading = false,
    this.error,
    this.canClaim = true,
    this.lastClaimTime,
  });

  DailyBonusState copyWith({
    DailyBonus? bonus,
    bool? isLoading,
    String? error,
    bool? canClaim,
    DateTime? lastClaimTime,
  }) {
    return DailyBonusState(
      bonus: bonus,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      canClaim: canClaim ?? this.canClaim,
      lastClaimTime: lastClaimTime ?? this.lastClaimTime,
    );
  }
}

// Daily Bonus Notifier
class DailyBonusNotifier extends StateNotifier<DailyBonusState> {
  DailyBonusNotifier() : super(const DailyBonusState()) {
    _checkDailyBonusAvailability();
  }

  void _checkDailyBonusAvailability() {
    // Check if 24 hours have passed since last claim
    if (state.lastClaimTime != null) {
      final now = DateTime.now();
      final timeDifference = now.difference(state.lastClaimTime!);
      final canClaim = timeDifference.inHours >= 24;
      state = state.copyWith(canClaim: canClaim);
    }
  }

  Future<void> claimDailyBonus() async {
    if (!state.canClaim) return;
    
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      // TODO: Implement API call for daily bonus
      // For now, simulate a bonus
      await Future.delayed(const Duration(seconds: 1));
      
      state = state.copyWith(
        isLoading: false,
        canClaim: false,
        lastClaimTime: DateTime.now(),
      );
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to claim daily bonus: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Shop Filter
class ShopFilter {
  final String? type;
  final bool showOnlyAvailable;
  final bool showOnlyDiscounted;
  final ShopSortBy sortBy;

  const ShopFilter({
    this.type,
    this.showOnlyAvailable = false,
    this.showOnlyDiscounted = false,
    this.sortBy = ShopSortBy.name,
  });

  ShopFilter copyWith({
    String? type,
    bool? showOnlyAvailable,
    bool? showOnlyDiscounted,
    ShopSortBy? sortBy,
  }) {
    return ShopFilter(
      type: type,
      showOnlyAvailable: showOnlyAvailable ?? this.showOnlyAvailable,
      showOnlyDiscounted: showOnlyDiscounted ?? this.showOnlyDiscounted,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}

// Shop Filter Notifier
class ShopFilterNotifier extends StateNotifier<ShopFilter> {
  ShopFilterNotifier() : super(const ShopFilter());

  void setType(String? type) {
    state = state.copyWith(type: type);
  }

  void setShowOnlyAvailable(bool showOnlyAvailable) {
    state = state.copyWith(showOnlyAvailable: showOnlyAvailable);
  }

  void setShowOnlyDiscounted(bool showOnlyDiscounted) {
    state = state.copyWith(showOnlyDiscounted: showOnlyDiscounted);
  }

  void setSortBy(ShopSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void clearFilters() {
    state = const ShopFilter();
  }
}

// Sort options
enum ShopSortBy {
  name,
  priceCoinsLow,
  priceCoinsHigh,
  priceGemsLow,
  priceGemsHigh,
}

extension ShopSortByExtension on ShopSortBy {
  String get label {
    switch (this) {
      case ShopSortBy.name:
        return 'Name';
      case ShopSortBy.priceCoinsLow:
        return 'Price (Coins ↑)';
      case ShopSortBy.priceCoinsHigh:
        return 'Price (Coins ↓)';
      case ShopSortBy.priceGemsLow:
        return 'Price (Gems ↑)';
      case ShopSortBy.priceGemsHigh:
        return 'Price (Gems ↓)';
    }
  }
}

// Purchase result from API service
class PurchaseResult {
  final bool success;
  final Map<String, dynamic> itemsReceived;
  final int playerCoins;
  final int playerGems;

  PurchaseResult({
    required this.success,
    required this.itemsReceived,
    required this.playerCoins,
    required this.playerGems,
  });

  factory PurchaseResult.fromJson(Map<String, dynamic> json) {
    return PurchaseResult(
      success: json['success'] ?? false,
      itemsReceived: json['itemsReceived'] ?? {},
      playerCoins: json['playerCoins'] ?? 0,
      playerGems: json['playerGems'] ?? 0,
    );
  }
}
