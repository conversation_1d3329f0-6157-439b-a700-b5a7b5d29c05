-- Initial card definitions for Rally Championship Manager
-- Driver Cards

INSERT INTO card_definitions (name, type, rarity, stats, abilities, description, flavor_text) VALUES
-- Legendary Drivers
('<PERSON> "Lightning" Rodriguez', 'driver', 'legendary', 
 '{"speed": 95, "handling": 92, "consistency": 88, "adaptability": 90}',
 '{"tarmac_master": 20, "night_owl": 25}',
 'World Rally Championship legend with unmatched speed on tarmac stages.',
 '"The road is my canvas, and speed is my brush."'),

('<PERSON> "Ice Queen" Volkov', 'driver', 'legendary',
 '{"speed": 88, "handling": 95, "consistency": 93, "adaptability": 92}',
 '{"weather_wizard": 15, "gravel_expert": 20}',
 'Russian rally master known for her precision in extreme weather conditions.',
 '"In the storm, I find my calm."'),

-- Epic Drivers
('<PERSON> "Thunder" Morrison', 'driver', 'epic',
 '{"speed": 85, "handling": 82, "consistency": 80, "adaptability": 83}',
 '{"gravel_expert": 20}',
 'American rally driver specializing in high-speed gravel stages.',
 '"Dirt roads are where legends are born."'),

('<PERSON> "Precision" Dubois', 'driver', 'epic',
 '{"speed": 80, "handling": 88, "consistency": 85, "adaptability": 82}',
 '{"tarmac_master": 20}',
 'French technical driving specialist with surgical precision.',
 '"Every corner is a puzzle to solve."'),

-- Rare Drivers
('Carlos "Steady" Santos', 'driver', 'rare',
 '{"speed": 75, "handling": 78, "consistency": 82, "adaptability": 76}',
 '{"consistency_boost": 10}',
 'Brazilian driver known for consistent performance across all conditions.',
 '"Slow and steady wins the rally."'),

('Yuki "Rising Sun" Tanaka', 'driver', 'rare',
 '{"speed": 78, "handling": 75, "consistency": 73, "adaptability": 80}',
 '{"night_owl": 25}',
 'Japanese driver with exceptional night driving skills.',
 '"The darkness reveals the true path."'),

-- Common Drivers
('Mike "Rookie" Johnson', 'driver', 'common',
 '{"speed": 65, "handling": 68, "consistency": 70, "adaptability": 62}',
 '{}',
 'Promising young driver starting his rally career.',
 '"Every champion was once a beginner."'),

('Anna "Steady" Kowalski', 'driver', 'common',
 '{"speed": 62, "handling": 70, "consistency": 72, "adaptability": 65}',
 '{}',
 'Reliable driver with solid fundamentals.',
 '"Consistency is the key to success."');

-- Co-Driver Cards
INSERT INTO card_definitions (name, type, rarity, stats, abilities, description, flavor_text) VALUES
-- Legendary Co-Drivers
('Roberto "Navigator" Silva', 'codriver', 'legendary',
 '{"pace_notes": 95, "route_knowledge": 92, "communication": 90, "experience": 88}',
 '{"pace_notes_expert": -10, "route_master": 5, "motivator": 10}',
 'Legendary co-driver with perfect pace note accuracy.',
 '"I see the road before we reach it."'),

-- Epic Co-Drivers
('Sarah "Voice" Mitchell', 'codriver', 'epic',
 '{"pace_notes": 85, "route_knowledge": 88, "communication": 90, "experience": 82}',
 '{"motivator": 10, "technical_specialist": 15}',
 'Master communicator who keeps drivers focused under pressure.',
 '"Clear mind, clear voice, clear victory."'),

-- Rare Co-Drivers
('Hans "Precision" Mueller', 'codriver', 'rare',
 '{"pace_notes": 78, "route_knowledge": 80, "communication": 75, "experience": 77}',
 '{"pace_notes_expert": -10}',
 'German co-driver known for meticulous pace notes.',
 '"Precision in every word, victory in every turn."'),

-- Common Co-Drivers
('Tom "Steady" Brown', 'codriver', 'common',
 '{"pace_notes": 65, "route_knowledge": 68, "communication": 70, "experience": 62}',
 '{}',
 'Reliable co-driver learning the ropes.',
 '"Together we go faster."');

-- Vehicle Cards
INSERT INTO card_definitions (name, type, rarity, stats, abilities, description, flavor_text) VALUES
-- Legendary Vehicles (WRC Class)
('Nordic Thunder WRC', 'vehicle', 'legendary',
 '{"top_speed": 95, "acceleration": 92, "handling": 90, "reliability": 85, "weather_rating": 95}',
 '{"snow_specialist": 25, "reliability_boost": 20}',
 'Ultimate WRC machine built for extreme conditions.',
 '"When nature fights back, we fight harder."'),

-- Epic Vehicles (R5 Class)
('Alpine Fury R5', 'vehicle', 'epic',
 '{"top_speed": 88, "acceleration": 85, "handling": 92, "reliability": 80, "weather_rating": 75}',
 '{"mountain_specialist": 20, "handling_boost": 15}',
 'High-performance R5 car designed for mountain stages.',
 '"Born in the mountains, bred for victory."'),

('Desert Storm R5', 'vehicle', 'epic',
 '{"top_speed": 90, "acceleration": 88, "handling": 82, "reliability": 85, "weather_rating": 70}',
 '{"heat_resistance": 20, "durability_focus": 15}',
 'Rugged R5 machine built to withstand harsh conditions.',
 '"Where others break, we endure."'),

-- Rare Vehicles (R3-R4 Class)
('Thunder Dynamics R4', 'vehicle', 'rare',
 '{"top_speed": 85, "acceleration": 82, "handling": 75, "reliability": 70, "weather_rating": 65}',
 '{"speed_boost": 15}',
 'High-speed R4 car with maximum velocity focus.',
 '"Speed is everything."'),

('Nordic Reliable R3', 'vehicle', 'rare',
 '{"top_speed": 75, "acceleration": 78, "handling": 80, "reliability": 85, "weather_rating": 80}',
 '{"reliability_boost": 20}',
 'Dependable R3 car that rarely breaks down.',
 '"Reliability wins rallies."'),

-- Common Vehicles (R1-R2 Class)
('Entry Runner R2', 'vehicle', 'common',
 '{"top_speed": 68, "acceleration": 70, "handling": 72, "reliability": 75, "weather_rating": 65}',
 '{}',
 'Balanced R2 car perfect for beginners.',
 '"Every journey starts with a single stage."'),

('Basic Rally R1', 'vehicle', 'common',
 '{"top_speed": 62, "acceleration": 65, "handling": 68, "reliability": 80, "weather_rating": 60}',
 '{}',
 'Entry-level rally car with solid reliability.',
 '"Simple, reliable, effective."');

-- Strategy Cards
INSERT INTO card_definitions (name, type, rarity, stats, abilities, description, flavor_text) VALUES
-- Setup Cards
('Aggressive Setup', 'strategy', 'common',
 '{"speed_bonus": 10, "crash_risk": 15}',
 '{"risk_reward": "high"}',
 'Push the car to its limits for maximum speed.',
 '"Fortune favors the bold."'),

('Conservative Setup', 'strategy', 'common',
 '{"speed_penalty": -5, "crash_risk": -20}',
 '{"safety_first": true}',
 'Play it safe to ensure you finish the rally.',
 '"Slow and steady wins the race."'),

('Balanced Setup', 'strategy', 'common',
 '{}',
 '{}',
 'Standard setup with no bonuses or penalties.',
 '"Sometimes the middle path is the wisest."'),

-- Tire Cards
('Soft Tires', 'strategy', 'rare',
 '{"speed_bonus": 15, "durability": -25}',
 '{"grip_advantage": 20}',
 'Maximum grip tires that wear out quickly.',
 '"Grip when you need it most."'),

('Hard Tires', 'strategy', 'common',
 '{"speed_penalty": -10, "durability": 30}',
 '{"longevity": true}',
 'Durable tires that last the entire rally.',
 '"Built to last."'),

('Weather Tires', 'strategy', 'epic',
 '{"weather_performance": 20}',
 '{"rain_master": 25, "snow_master": 25}',
 'Specialized tires for adverse weather conditions.',
 '"When the weather turns, we turn up."'),

-- Emergency Cards
('Repair Kit', 'strategy', 'rare',
 '{}',
 '{"fix_damage": true}',
 'Emergency repairs to fix mechanical damage.',
 '"A good mechanic is worth their weight in gold."'),

('Spare Parts', 'strategy', 'epic',
 '{}',
 '{"prevent_failure": true}',
 'Prevent one mechanical failure during the rally.',
 '"Preparation prevents poor performance."'),

('Weather Report', 'strategy', 'rare',
 '{}',
 '{"see_conditions": true}',
 'Get advance warning of upcoming stage conditions.',
 '"Knowledge is power."'),

('Pace Calculator', 'strategy', 'epic',
 '{}',
 '{"optimal_times": true}',
 'Calculate optimal stage times for perfect pacing.',
 '"Precision timing wins rallies."');
