const { query, beginTransaction, commitTransaction, rollbackTransaction } = require('../database/connection');
const { getLevelFromXP, getXPToNextLevel } = require('../utils/helpers');

class Player {
  constructor(data) {
    this.id = data.id;
    this.username = data.username;
    this.email = data.email;
    this.coins = data.coins;
    this.gems = data.gems;
    this.level = data.level;
    this.xp = data.xp;
    this.createdAt = data.created_at;
    this.lastLogin = data.last_login;
    this.isActive = data.is_active;
  }

  // Find player by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM players WHERE id = $1 AND is_active = true',
      [id]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Player(result.rows[0]);
  }

  // Find player by email
  static async findByEmail(email) {
    const result = await query(
      'SELECT * FROM players WHERE email = $1 AND is_active = true',
      [email]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Player(result.rows[0]);
  }

  // Find player by username
  static async findByUsername(username) {
    const result = await query(
      'SELECT * FROM players WHERE username = $1 AND is_active = true',
      [username]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Player(result.rows[0]);
  }

  // Create new player
  static async create(userData) {
    const { username, email, passwordHash, coins = 1000, gems = 100 } = userData;
    
    const result = await query(
      `INSERT INTO players (username, email, password_hash, coins, gems, level, xp) 
       VALUES ($1, $2, $3, $4, $5, 1, 0) 
       RETURNING *`,
      [username, email, passwordHash, coins, gems]
    );
    
    return new Player(result.rows[0]);
  }

  // Update player data
  async update(updates) {
    const allowedFields = ['username', 'email', 'coins', 'gems', 'level', 'xp', 'last_login'];
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field)) {
        updateFields.push(`${field} = $${paramCount}`);
        values.push(value);
        paramCount++;
      }
    }

    if (updateFields.length === 0) {
      return this;
    }

    values.push(this.id);
    const result = await query(
      `UPDATE players SET ${updateFields.join(', ')}, updated_at = NOW() 
       WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length > 0) {
      Object.assign(this, result.rows[0]);
    }

    return this;
  }

  // Add coins to player
  async addCoins(amount) {
    if (amount <= 0) return this;
    
    const result = await query(
      'UPDATE players SET coins = coins + $1 WHERE id = $2 RETURNING coins',
      [amount, this.id]
    );
    
    this.coins = result.rows[0].coins;
    return this;
  }

  // Spend coins (returns false if insufficient funds)
  async spendCoins(amount) {
    if (amount <= 0) return true;
    
    const result = await query(
      'UPDATE players SET coins = coins - $1 WHERE id = $2 AND coins >= $1 RETURNING coins',
      [amount, this.id]
    );
    
    if (result.rows.length === 0) {
      return false; // Insufficient funds
    }
    
    this.coins = result.rows[0].coins;
    return true;
  }

  // Add gems to player
  async addGems(amount) {
    if (amount <= 0) return this;
    
    const result = await query(
      'UPDATE players SET gems = gems + $1 WHERE id = $2 RETURNING gems',
      [amount, this.id]
    );
    
    this.gems = result.rows[0].gems;
    return this;
  }

  // Spend gems (returns false if insufficient funds)
  async spendGems(amount) {
    if (amount <= 0) return true;
    
    const result = await query(
      'UPDATE players SET gems = gems - $1 WHERE id = $2 AND gems >= $1 RETURNING gems',
      [amount, this.id]
    );
    
    if (result.rows.length === 0) {
      return false; // Insufficient funds
    }
    
    this.gems = result.rows[0].gems;
    return true;
  }

  // Add experience points and handle level ups
  async addXP(amount) {
    if (amount <= 0) return this;
    
    const newXP = this.xp + amount;
    const newLevel = getLevelFromXP(newXP);
    const leveledUp = newLevel > this.level;
    
    const result = await query(
      'UPDATE players SET xp = $1, level = $2 WHERE id = $3 RETURNING *',
      [newXP, newLevel, this.id]
    );
    
    Object.assign(this, result.rows[0]);
    
    return {
      player: this,
      leveledUp,
      previousLevel: leveledUp ? newLevel - 1 : newLevel,
      newLevel
    };
  }

  // Get player statistics
  async getStats() {
    const rallyStats = await query(`
      SELECT 
        COUNT(*) as total_rallies,
        AVG(final_position) as avg_position,
        MIN(final_position) as best_position,
        SUM(championship_points) as total_points,
        MIN(total_time) as best_time
      FROM rally_results 
      WHERE player_id = $1
    `, [this.id]);

    const cardStats = await query(`
      SELECT 
        COUNT(*) as total_cards,
        COUNT(CASE WHEN cd.rarity = 'legendary' THEN 1 END) as legendary_cards,
        COUNT(CASE WHEN cd.rarity = 'epic' THEN 1 END) as epic_cards,
        COUNT(CASE WHEN cd.rarity = 'rare' THEN 1 END) as rare_cards,
        COUNT(CASE WHEN cd.rarity = 'common' THEN 1 END) as common_cards
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1
    `, [this.id]);

    return {
      rally: rallyStats.rows[0],
      cards: cardStats.rows[0],
      level: this.level,
      xp: this.xp,
      xpToNext: getXPToNextLevel(this.xp)
    };
  }

  // Get player's card collection
  async getCardCollection() {
    const result = await query(`
      SELECT 
        pc.id as player_card_id,
        pc.level,
        pc.experience,
        pc.obtained_at,
        pc.is_favorite,
        cd.*
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1
      ORDER BY cd.rarity DESC, cd.name ASC
    `, [this.id]);

    return result.rows;
  }

  // Deactivate player account
  async deactivate() {
    await query(
      'UPDATE players SET is_active = false WHERE id = $1',
      [this.id]
    );
    
    this.isActive = false;
    return this;
  }

  // Convert to JSON (exclude sensitive data)
  toJSON() {
    return {
      id: this.id,
      username: this.username,
      email: this.email,
      coins: this.coins,
      gems: this.gems,
      level: this.level,
      xp: this.xp,
      createdAt: this.createdAt,
      lastLogin: this.lastLogin
    };
  }
}

module.exports = Player;
