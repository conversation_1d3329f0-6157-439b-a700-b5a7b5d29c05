const { query } = require('../database/connection');
const { weightedRandom, randomChoice } = require('../utils/helpers');
const { PACK_PROBABILITIES, CARD_RARITIES, CARD_TYPES } = require('../utils/constants');

class CardGenerator {
  constructor() {
    this.cardCache = {
      byRarity: {},
      byType: {},
      all: []
    };
    this.cacheExpiry = null;
  }

  // Refresh card cache
  async refreshCache() {
    const now = Date.now();
    
    // Cache for 5 minutes
    if (this.cacheExpiry && now < this.cacheExpiry) {
      return;
    }

    console.log('🔄 Refreshing card cache...');
    
    const result = await query(`
      SELECT id, name, type, rarity, stats, abilities, image_url, description, flavor_text
      FROM card_definitions 
      WHERE is_active = true
    `);

    this.cardCache.all = result.rows;
    this.cardCache.byRarity = {};
    this.cardCache.byType = {};

    // Group by rarity
    for (const rarity of Object.values(CARD_RARITIES)) {
      this.cardCache.byRarity[rarity] = result.rows.filter(card => card.rarity === rarity);
    }

    // Group by type
    for (const type of Object.values(CARD_TYPES)) {
      this.cardCache.byType[type] = result.rows.filter(card => card.type === type);
    }

    this.cacheExpiry = now + (5 * 60 * 1000); // 5 minutes
    console.log(`✅ Card cache refreshed: ${result.rows.length} cards loaded`);
  }

  // Generate cards for a pack
  async generatePack(packType, numCards, guaranteedRarity = null, excludeIds = []) {
    await this.refreshCache();
    
    const packConfig = PACK_PROBABILITIES[packType.toUpperCase()];
    if (!packConfig) {
      throw new Error(`Unknown pack type: ${packType}`);
    }

    const generatedCards = [];
    const usedCardIds = new Set(excludeIds);

    for (let i = 0; i < numCards; i++) {
      let rarity;
      
      // First card might be guaranteed rarity
      if (i === 0 && guaranteedRarity) {
        rarity = guaranteedRarity;
      } else {
        rarity = weightedRandom(packConfig);
      }

      const card = await this.selectRandomCard(rarity, usedCardIds);
      if (card) {
        generatedCards.push(card);
        usedCardIds.add(card.id);
      }
    }

    return generatedCards;
  }

  // Generate specific card types (for special packs)
  async generateSpecificCards(cardType, numCards, rarityWeights = null, excludeIds = []) {
    await this.refreshCache();
    
    const availableCards = this.cardCache.byType[cardType] || [];
    if (availableCards.length === 0) {
      throw new Error(`No cards available for type: ${cardType}`);
    }

    const generatedCards = [];
    const usedCardIds = new Set(excludeIds);

    for (let i = 0; i < numCards; i++) {
      let selectedCard;
      
      if (rarityWeights) {
        // Select based on rarity weights
        const rarity = weightedRandom(rarityWeights);
        const cardsOfRarity = availableCards.filter(card => 
          card.rarity === rarity && !usedCardIds.has(card.id)
        );
        selectedCard = randomChoice(cardsOfRarity);
      } else {
        // Random selection from available cards
        const availableForSelection = availableCards.filter(card => !usedCardIds.has(card.id));
        selectedCard = randomChoice(availableForSelection);
      }

      if (selectedCard) {
        generatedCards.push(selectedCard);
        usedCardIds.add(selectedCard.id);
      }
    }

    return generatedCards;
  }

  // Select random card of specific rarity
  async selectRandomCard(rarity, excludeIds = new Set()) {
    const availableCards = (this.cardCache.byRarity[rarity] || [])
      .filter(card => !excludeIds.has(card.id));
    
    if (availableCards.length === 0) {
      console.warn(`No available cards for rarity: ${rarity}`);
      return null;
    }

    return randomChoice(availableCards);
  }

  // Generate starter deck for new players
  async generateStarterDeck() {
    await this.refreshCache();
    
    const starterDeck = {
      drivers: [],
      codrivers: [],
      vehicles: [],
      strategyCards: []
    };

    // Generate 2 common drivers
    starterDeck.drivers = await this.generateSpecificCards('driver', 2, {
      [CARD_RARITIES.COMMON]: 80,
      [CARD_RARITIES.RARE]: 20
    });

    // Generate 2 common co-drivers
    starterDeck.codrivers = await this.generateSpecificCards('codriver', 2, {
      [CARD_RARITIES.COMMON]: 80,
      [CARD_RARITIES.RARE]: 20
    });

    // Generate 2 common vehicles
    starterDeck.vehicles = await this.generateSpecificCards('vehicle', 2, {
      [CARD_RARITIES.COMMON]: 80,
      [CARD_RARITIES.RARE]: 20
    });

    // Generate 5 strategy cards
    starterDeck.strategyCards = await this.generateSpecificCards('strategy', 5, {
      [CARD_RARITIES.COMMON]: 70,
      [CARD_RARITIES.RARE]: 30
    });

    return starterDeck;
  }

  // Generate daily bonus card
  async generateDailyBonus() {
    await this.refreshCache();
    
    // Daily bonus has better odds than normal packs
    const dailyWeights = {
      [CARD_RARITIES.COMMON]: 50,
      [CARD_RARITIES.RARE]: 35,
      [CARD_RARITIES.EPIC]: 13,
      [CARD_RARITIES.LEGENDARY]: 2
    };

    const rarity = weightedRandom(dailyWeights);
    return await this.selectRandomCard(rarity);
  }

  // Generate achievement reward cards
  async generateAchievementReward(achievementType, achievementTier) {
    await this.refreshCache();
    
    let rarityWeights;
    
    switch (achievementTier) {
      case 'bronze':
        rarityWeights = {
          [CARD_RARITIES.COMMON]: 60,
          [CARD_RARITIES.RARE]: 35,
          [CARD_RARITIES.EPIC]: 5
        };
        break;
      case 'silver':
        rarityWeights = {
          [CARD_RARITIES.RARE]: 50,
          [CARD_RARITIES.EPIC]: 40,
          [CARD_RARITIES.LEGENDARY]: 10
        };
        break;
      case 'gold':
        rarityWeights = {
          [CARD_RARITIES.EPIC]: 60,
          [CARD_RARITIES.LEGENDARY]: 40
        };
        break;
      default:
        rarityWeights = {
          [CARD_RARITIES.COMMON]: 70,
          [CARD_RARITIES.RARE]: 25,
          [CARD_RARITIES.EPIC]: 5
        };
    }

    const rarity = weightedRandom(rarityWeights);
    return await this.selectRandomCard(rarity);
  }

  // Generate cards for special events
  async generateEventReward(eventType, playerPerformance) {
    await this.refreshCache();
    
    let rarityWeights;
    
    // Better performance = better cards
    if (playerPerformance <= 3) { // Top 3 finish
      rarityWeights = {
        [CARD_RARITIES.RARE]: 40,
        [CARD_RARITIES.EPIC]: 45,
        [CARD_RARITIES.LEGENDARY]: 15
      };
    } else if (playerPerformance <= 10) { // Top 10 finish
      rarityWeights = {
        [CARD_RARITIES.COMMON]: 20,
        [CARD_RARITIES.RARE]: 50,
        [CARD_RARITIES.EPIC]: 25,
        [CARD_RARITIES.LEGENDARY]: 5
      };
    } else { // Participation reward
      rarityWeights = {
        [CARD_RARITIES.COMMON]: 50,
        [CARD_RARITIES.RARE]: 40,
        [CARD_RARITIES.EPIC]: 10
      };
    }

    const rarity = weightedRandom(rarityWeights);
    return await this.selectRandomCard(rarity);
  }

  // Calculate pack value (for display purposes)
  calculatePackValue(cards) {
    const rarityValues = {
      [CARD_RARITIES.COMMON]: 10,
      [CARD_RARITIES.RARE]: 50,
      [CARD_RARITIES.EPIC]: 200,
      [CARD_RARITIES.LEGENDARY]: 1000
    };

    return cards.reduce((total, card) => {
      return total + (rarityValues[card.rarity] || 0);
    }, 0);
  }

  // Get pack statistics
  getPackStats(cards) {
    const stats = {
      total: cards.length,
      common: 0,
      rare: 0,
      epic: 0,
      legendary: 0,
      byType: {
        driver: 0,
        codriver: 0,
        vehicle: 0,
        strategy: 0
      }
    };

    cards.forEach(card => {
      stats[card.rarity]++;
      stats.byType[card.type]++;
    });

    return stats;
  }

  // Check for duplicate protection
  async checkDuplicateProtection(playerId, newCards) {
    const playerCardIds = await query(`
      SELECT card_id FROM player_cards WHERE player_id = $1
    `, [playerId]);

    const ownedCardIds = new Set(playerCardIds.rows.map(row => row.card_id));
    
    return newCards.map(card => ({
      ...card,
      isDuplicate: ownedCardIds.has(card.id),
      dustValue: this.calculateDustValue(card.rarity)
    }));
  }

  // Calculate dust value for duplicate cards
  calculateDustValue(rarity) {
    const dustValues = {
      [CARD_RARITIES.COMMON]: 5,
      [CARD_RARITIES.RARE]: 20,
      [CARD_RARITIES.EPIC]: 100,
      [CARD_RARITIES.LEGENDARY]: 400
    };

    return dustValues[rarity] || 0;
  }

  // Get card rarity distribution for pack type
  getPackRarityDistribution(packType) {
    return PACK_PROBABILITIES[packType.toUpperCase()] || {};
  }

  // Validate pack configuration
  validatePackConfig(packConfig) {
    const requiredFields = ['cards', 'probabilities'];
    
    for (const field of requiredFields) {
      if (!packConfig[field]) {
        throw new Error(`Pack configuration missing required field: ${field}`);
      }
    }

    // Validate probabilities sum to 100
    const totalProbability = Object.values(packConfig.probabilities).reduce((sum, prob) => sum + prob, 0);
    if (Math.abs(totalProbability - 100) > 0.01) {
      throw new Error(`Pack probabilities must sum to 100, got ${totalProbability}`);
    }

    return true;
  }

  // Clear cache (for testing or manual refresh)
  clearCache() {
    this.cardCache = {
      byRarity: {},
      byType: {},
      all: []
    };
    this.cacheExpiry = null;
    console.log('🗑️ Card cache cleared');
  }
}

module.exports = new CardGenerator();
