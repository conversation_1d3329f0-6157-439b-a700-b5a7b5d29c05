const fs = require('fs');
const path = require('path');
const { query, testConnection } = require('./connection');

// Migration tracking table
const createMigrationsTable = async () => {
  await query(`
    CREATE TABLE IF NOT EXISTS migrations (
      id SERIAL PRIMARY KEY,
      filename VARCHAR(255) NOT NULL UNIQUE,
      executed_at TIMESTAMP DEFAULT NOW()
    )
  `);
};

// Get list of executed migrations
const getExecutedMigrations = async () => {
  try {
    const result = await query('SELECT filename FROM migrations ORDER BY id');
    return result.rows.map(row => row.filename);
  } catch (error) {
    // If migrations table doesn't exist, return empty array
    return [];
  }
};

// Execute a single migration file
const executeMigration = async (filename) => {
  const filePath = path.join(__dirname, 'migrations', filename);
  const sql = fs.readFileSync(filePath, 'utf8');
  
  console.log(`🔄 Executing migration: ${filename}`);
  
  try {
    // Execute the migration SQL
    await query(sql);
    
    // Record the migration as executed
    await query(
      'INSERT INTO migrations (filename) VALUES ($1)',
      [filename]
    );
    
    console.log(`✅ Migration completed: ${filename}`);
  } catch (error) {
    console.error(`❌ Migration failed: ${filename}`);
    throw error;
  }
};

// Run all pending migrations
const runMigrations = async () => {
  console.log('🚀 Starting database migrations...');
  
  // Test database connection
  const isConnected = await testConnection();
  if (!isConnected) {
    console.error('❌ Database connection failed. Please check your configuration.');
    process.exit(1);
  }
  
  // Create migrations tracking table
  await createMigrationsTable();
  
  // Get list of migration files
  const migrationsDir = path.join(__dirname, 'migrations');
  const migrationFiles = fs.readdirSync(migrationsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  if (migrationFiles.length === 0) {
    console.log('📝 No migration files found.');
    return;
  }
  
  // Get executed migrations
  const executedMigrations = await getExecutedMigrations();
  
  // Find pending migrations
  const pendingMigrations = migrationFiles.filter(
    file => !executedMigrations.includes(file)
  );
  
  if (pendingMigrations.length === 0) {
    console.log('✅ All migrations are up to date.');
    return;
  }
  
  console.log(`📋 Found ${pendingMigrations.length} pending migrations:`);
  pendingMigrations.forEach(file => console.log(`   - ${file}`));
  
  // Execute pending migrations
  for (const filename of pendingMigrations) {
    await executeMigration(filename);
  }
  
  console.log('🎉 All migrations completed successfully!');
};

// Reset database (drop all tables and re-run migrations)
const resetDatabase = async () => {
  console.log('⚠️  RESETTING DATABASE - This will delete all data!');
  
  try {
    // Drop all tables (in reverse dependency order)
    const dropTables = [
      'player_achievements',
      'achievements', 
      'player_decks',
      'leaderboards',
      'purchases',
      'shop_items',
      'rally_results',
      'rally_events',
      'player_cards',
      'card_definitions',
      'players',
      'migrations'
    ];
    
    for (const table of dropTables) {
      await query(`DROP TABLE IF EXISTS ${table} CASCADE`);
      console.log(`🗑️  Dropped table: ${table}`);
    }
    
    console.log('🔄 Running migrations after reset...');
    await runMigrations();
    
  } catch (error) {
    console.error('❌ Database reset failed:', error);
    throw error;
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'up':
        await runMigrations();
        break;
      case 'reset':
        await resetDatabase();
        break;
      default:
        console.log('Usage:');
        console.log('  node migrate.js up    - Run pending migrations');
        console.log('  node migrate.js reset - Reset database and run all migrations');
        break;
    }
  } catch (error) {
    console.error('❌ Migration error:', error);
    process.exit(1);
  }
  
  process.exit(0);
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runMigrations,
  resetDatabase
};
