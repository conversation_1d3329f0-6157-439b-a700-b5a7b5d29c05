// Simplified models without code generation for testing

class User {
  final int id;
  final String username;
  final String email;
  final int coins;
  final int gems;
  final int level;
  final int xp;
  final DateTime? createdAt;
  final DateTime? lastLogin;

  const User({
    required this.id,
    required this.username,
    required this.email,
    required this.coins,
    required this.gems,
    required this.level,
    required this.xp,
    this.createdAt,
    this.lastLogin,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] ?? 0,
      username: json['username'] ?? '',
      email: json['email'] ?? '',
      coins: json['coins'] ?? 0,
      gems: json['gems'] ?? 0,
      level: json['level'] ?? 1,
      xp: json['xp'] ?? 0,
      createdAt: json['createdAt'] != null ? DateTime.parse(json['createdAt']) : null,
      lastLogin: json['lastLogin'] != null ? DateTime.parse(json['lastLogin']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'username': username,
      'email': email,
      'coins': coins,
      'gems': gems,
      'level': level,
      'xp': xp,
      'createdAt': createdAt?.toIso8601String(),
      'lastLogin': lastLogin?.toIso8601String(),
    };
  }

  User copyWith({
    int? id,
    String? username,
    String? email,
    int? coins,
    int? gems,
    int? level,
    int? xp,
    DateTime? createdAt,
    DateTime? lastLogin,
  }) {
    return User(
      id: id ?? this.id,
      username: username ?? this.username,
      email: email ?? this.email,
      coins: coins ?? this.coins,
      gems: gems ?? this.gems,
      level: level ?? this.level,
      xp: xp ?? this.xp,
      createdAt: createdAt ?? this.createdAt,
      lastLogin: lastLogin ?? this.lastLogin,
    );
  }
}

class GameCard {
  final int id;
  final String name;
  final String type;
  final String rarity;
  final Map<String, dynamic> stats;
  final Map<String, dynamic>? abilities;
  final String? imageUrl;
  final String? description;
  final String? flavorText;
  final bool isActive;
  final DateTime? createdAt;

  const GameCard({
    required this.id,
    required this.name,
    required this.type,
    required this.rarity,
    required this.stats,
    this.abilities,
    this.imageUrl,
    this.description,
    this.flavorText,
    this.isActive = true,
    this.createdAt,
  });

  factory GameCard.fromJson(Map<String, dynamic> json) {
    return GameCard(
      id: json['id'] ?? 0,
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      rarity: json['rarity'] ?? 'common',
      stats: json['stats'] ?? {},
      abilities: json['abilities'],
      imageUrl: json['image_url'],
      description: json['description'],
      flavorText: json['flavor_text'],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'rarity': rarity,
      'stats': stats,
      'abilities': abilities,
      'image_url': imageUrl,
      'description': description,
      'flavor_text': flavorText,
      'is_active': isActive,
      'created_at': createdAt?.toIso8601String(),
    };
  }

  // Get primary stats for display
  List<CardStat> get primaryStats {
    return stats.entries.map((entry) {
      return CardStat(entry.key, entry.value is int ? entry.value : 0);
    }).toList();
  }
}

class CardStat {
  final String name;
  final int value;

  const CardStat(this.name, this.value);
}

class PlayerCard {
  final int playerCardId;
  final int playerId;
  final int cardId;
  final int level;
  final int experience;
  final DateTime obtainedAt;
  final bool isFavorite;
  final GameCard? card;
  final Map<String, dynamic>? effectiveStats;

  const PlayerCard({
    required this.playerCardId,
    required this.playerId,
    required this.cardId,
    required this.level,
    required this.experience,
    required this.obtainedAt,
    required this.isFavorite,
    this.card,
    this.effectiveStats,
  });

  factory PlayerCard.fromJson(Map<String, dynamic> json) {
    return PlayerCard(
      playerCardId: json['player_card_id'] ?? 0,
      playerId: json['player_id'] ?? 0,
      cardId: json['card_id'] ?? 0,
      level: json['level'] ?? 1,
      experience: json['experience'] ?? 0,
      obtainedAt: json['obtained_at'] != null 
        ? DateTime.parse(json['obtained_at']) 
        : DateTime.now(),
      isFavorite: json['is_favorite'] ?? false,
      card: json['card'] != null ? GameCard.fromJson(json['card']) : null,
      effectiveStats: json['effective_stats'],
    );
  }

  PlayerCard copyWith({
    int? playerCardId,
    int? playerId,
    int? cardId,
    int? level,
    int? experience,
    DateTime? obtainedAt,
    bool? isFavorite,
    GameCard? card,
    Map<String, dynamic>? effectiveStats,
  }) {
    return PlayerCard(
      playerCardId: playerCardId ?? this.playerCardId,
      playerId: playerId ?? this.playerId,
      cardId: cardId ?? this.cardId,
      level: level ?? this.level,
      experience: experience ?? this.experience,
      obtainedAt: obtainedAt ?? this.obtainedAt,
      isFavorite: isFavorite ?? this.isFavorite,
      card: card ?? this.card,
      effectiveStats: effectiveStats ?? this.effectiveStats,
    );
  }

  // Helper methods
  int getEffectiveStatValue(String statName) {
    if (effectiveStats != null && effectiveStats!.containsKey(statName)) {
      return effectiveStats![statName] ?? 0;
    }
    if (card != null && card!.stats.containsKey(statName)) {
      return card!.stats[statName] ?? 0;
    }
    return 0;
  }

  double get experienceProgress {
    final xpForNextLevel = experienceToNextLevel + experience;
    return xpForNextLevel > 0 ? experience / xpForNextLevel : 0.0;
  }

  int get experienceToNextLevel {
    return (level * 100) - experience;
  }

  int get upgradeCost {
    return level * 50;
  }
}

class Deck {
  final int? id;
  final String name;
  final int driverCardId;
  final int codriverCardId;
  final int vehicleCardId;
  final List<int> strategyCards;
  final bool isActive;
  final DateTime? createdAt;

  const Deck({
    this.id,
    required this.name,
    required this.driverCardId,
    required this.codriverCardId,
    required this.vehicleCardId,
    required this.strategyCards,
    this.isActive = true,
    this.createdAt,
  });

  factory Deck.fromJson(Map<String, dynamic> json) {
    return Deck(
      id: json['id'],
      name: json['name'] ?? '',
      driverCardId: json['driver_card_id'] ?? 0,
      codriverCardId: json['codriver_card_id'] ?? 0,
      vehicleCardId: json['vehicle_card_id'] ?? 0,
      strategyCards: (json['strategy_cards'] as List?)?.cast<int>() ?? [],
      isActive: json['is_active'] ?? true,
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
    );
  }

  Deck copyWith({
    int? id,
    String? name,
    int? driverCardId,
    int? codriverCardId,
    int? vehicleCardId,
    List<int>? strategyCards,
    bool? isActive,
    DateTime? createdAt,
  }) {
    return Deck(
      id: id ?? this.id,
      name: name ?? this.name,
      driverCardId: driverCardId ?? this.driverCardId,
      codriverCardId: codriverCardId ?? this.codriverCardId,
      vehicleCardId: vehicleCardId ?? this.vehicleCardId,
      strategyCards: strategyCards ?? this.strategyCards,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}
