const { randomBetween, randomFloat, calculateStageTime, calculateCrashProbability } = require('../utils/helpers');
const { RALLY_REWARDS, WEATHER_CONDITIONS, STAGE_TYPES } = require('../utils/constants');

class RallySimulator {
  constructor() {
    this.weatherEffects = {
      clear: { timeMultiplier: 1.0, crashMultiplier: 1.0 },
      rain: { timeMultiplier: 1.15, crashMultiplier: 1.5 },
      snow: { timeMultiplier: 1.25, crashMultiplier: 2.0 },
      fog: { timeMultiplier: 1.1, crashMultiplier: 1.3 }
    };

    this.surfaceEffects = {
      tarmac: { timeMultiplier: 1.0, crashMultiplier: 1.0 },
      gravel: { timeMultiplier: 1.1, crashMultiplier: 1.2 },
      snow: { timeMultiplier: 1.3, crashMultiplier: 1.8 },
      mixed: { timeMultiplier: 1.05, crashMultiplier: 1.1 }
    };
  }

  // Main rally simulation method
  async simulateRally(rallyData, playerDeck, riskChoices = []) {
    const { stages } = rallyData;
    const stageResults = [];
    let totalTime = 0;
    let totalDamage = 0;
    let crashed = false;
    let mechanicalFailures = 0;

    // Extract card stats with level bonuses
    const driverStats = this.getEffectiveStats(playerDeck.driver);
    const codriverStats = this.getEffectiveStats(playerDeck.codriver);
    const vehicleStats = this.getEffectiveStats(playerDeck.vehicle);
    const strategyCards = playerDeck.strategyCards || [];

    for (let i = 0; i < stages.length; i++) {
      const stage = stages[i];
      const riskLevel = riskChoices[i] || 'balanced';
      
      const stageResult = await this.simulateStage(
        stage,
        driverStats,
        codriverStats,
        vehicleStats,
        strategyCards,
        riskLevel,
        totalDamage
      );

      stageResults.push(stageResult);
      totalTime += stageResult.time;
      totalDamage += stageResult.damageIncurred;
      
      if (stageResult.crashed) {
        crashed = true;
      }
      
      if (stageResult.mechanicalFailure) {
        mechanicalFailures++;
      }
    }

    // Calculate final position based on total time and performance
    const finalPosition = this.calculateFinalPosition(totalTime, crashed, mechanicalFailures);
    
    // Calculate rewards
    const rewards = this.calculateRewards(finalPosition, rallyData.rewards);

    return {
      stageResults,
      totalTime,
      finalPosition,
      rewards,
      crashed,
      totalDamage,
      mechanicalFailures,
      summary: this.generateRallySummary(stageResults, finalPosition)
    };
  }

  // Simulate individual stage
  async simulateStage(stage, driverStats, codriverStats, vehicleStats, strategyCards, riskLevel, currentDamage) {
    // Base time calculation (1 minute per km as baseline)
    let baseTime = stage.length * 60000; // milliseconds
    
    // Apply difficulty multiplier
    const difficultyMultiplier = 1 + (stage.difficulty - 1) * 0.15;
    baseTime *= difficultyMultiplier;

    // Apply driver skill
    const driverSkillFactor = this.calculateDriverSkillFactor(driverStats, stage);
    let stageTime = baseTime * driverSkillFactor;

    // Apply co-driver effects
    const codriverBonus = this.calculateCodriverBonus(codriverStats, stage);
    stageTime *= codriverBonus;

    // Apply vehicle performance
    const vehiclePerformance = this.calculateVehiclePerformance(vehicleStats, stage);
    stageTime *= vehiclePerformance;

    // Apply weather and surface effects
    const weatherEffect = this.weatherEffects[stage.weather] || this.weatherEffects.clear;
    const surfaceEffect = this.surfaceEffects[stage.surface] || this.surfaceEffects.tarmac;
    
    stageTime *= weatherEffect.timeMultiplier;
    stageTime *= surfaceEffect.timeMultiplier;

    // Apply strategy cards
    const strategyEffect = this.applyStrategyCards(strategyCards, stage);
    stageTime *= strategyEffect.timeMultiplier;

    // Apply risk level
    const riskEffect = this.calculateRiskEffect(riskLevel);
    stageTime *= riskEffect.timeMultiplier;

    // Apply damage penalty
    if (currentDamage > 0) {
      const damagePenalty = 1 + (currentDamage / 200); // 0.5% per damage point
      stageTime *= damagePenalty;
    }

    // Add random variation (±3%)
    const randomVariation = randomFloat(0.97, 1.03);
    stageTime *= randomVariation;

    // Calculate crash probability
    let crashChance = this.calculateCrashChance(
      driverStats,
      vehicleStats,
      stage,
      riskLevel,
      weatherEffect,
      surfaceEffect
    );

    // Apply co-driver crash reduction
    if (codriverStats.abilities && codriverStats.abilities.pace_notes_expert) {
      crashChance *= (1 + codriverStats.abilities.pace_notes_expert / 100);
    }

    // Apply strategy card effects to crash chance
    crashChance *= strategyEffect.crashMultiplier;

    // Determine if crash occurred
    const crashRoll = Math.random();
    const crashed = crashRoll < crashChance;

    let damageIncurred = 0;
    let mechanicalFailure = false;

    if (crashed) {
      // Add crash penalty time (15-45 seconds)
      const crashPenalty = randomBetween(15000, 45000);
      stageTime += crashPenalty;
      
      // Add damage
      damageIncurred = randomBetween(15, 35);
      
      // Check for mechanical failure
      const failureChance = Math.max(0.02, crashChance * 2);
      mechanicalFailure = Math.random() < failureChance;
      
      if (mechanicalFailure) {
        // Major time penalty for mechanical failure
        stageTime += randomBetween(60000, 180000); // 1-3 minutes
        damageIncurred += randomBetween(20, 40);
      }
    } else {
      // Small chance of mechanical failure even without crash
      const baseFailureChance = Math.max(0.005, (100 - vehicleStats.stats.reliability) / 2000);
      mechanicalFailure = Math.random() < baseFailureChance;
      
      if (mechanicalFailure) {
        stageTime += randomBetween(30000, 90000); // 30 seconds - 1.5 minutes
        damageIncurred = randomBetween(5, 15);
      }
    }

    // Calculate stage position (simplified)
    const stagePosition = this.calculateStagePosition(stageTime, stage.difficulty);

    return {
      stageIndex: stage.index || 0,
      stageName: stage.name,
      time: Math.round(stageTime),
      crashed,
      mechanicalFailure,
      damageIncurred,
      stagePosition,
      weather: stage.weather,
      surface: stage.surface,
      riskLevel,
      notes: this.generateStageNotes(crashed, mechanicalFailure, stagePosition)
    };
  }

  // Calculate driver skill factor based on stage type
  calculateDriverSkillFactor(driverStats, stage) {
    const stats = driverStats.stats;
    let skillFactor = 1.0;

    // Base skill calculation
    const avgSkill = (stats.speed + stats.handling + stats.consistency + stats.adaptability) / 4;
    skillFactor = 1.2 - (avgSkill / 100) * 0.4; // Range: 0.8 to 1.2

    // Apply specializations
    const abilities = driverStats.abilities || {};
    
    if (stage.surface === 'tarmac' && abilities.tarmac_master) {
      skillFactor *= (1 - abilities.tarmac_master / 100);
    }
    
    if (stage.surface === 'gravel' && abilities.gravel_expert) {
      skillFactor *= (1 - abilities.gravel_expert / 100);
    }
    
    if ((stage.weather === 'rain' || stage.weather === 'snow') && abilities.weather_wizard) {
      skillFactor *= (1 - abilities.weather_wizard / 100);
    }
    
    if (stage.time === 'night' && abilities.night_owl) {
      skillFactor *= (1 - abilities.night_owl / 100);
    }

    return Math.max(0.6, Math.min(1.4, skillFactor));
  }

  // Calculate co-driver bonus
  calculateCodriverBonus(codriverStats, stage) {
    const stats = codriverStats.stats;
    const abilities = codriverStats.abilities || {};
    
    let bonus = 1.0;
    
    // Base co-driver skill
    const avgSkill = (stats.pace_notes + stats.route_knowledge + stats.communication) / 3;
    bonus *= (1 - (avgSkill - 50) / 500); // Small but meaningful impact
    
    // Apply abilities
    if (abilities.route_master && stage.type === 'technical') {
      bonus *= (1 - abilities.route_master / 100);
    }
    
    return Math.max(0.95, Math.min(1.05, bonus));
  }

  // Calculate vehicle performance
  calculateVehiclePerformance(vehicleStats, stage) {
    const stats = vehicleStats.stats;
    let performance = 1.0;
    
    // Stage type specific performance
    switch (stage.type) {
      case 'sprint':
      case 'high_speed':
        performance = 1.1 - (stats.top_speed / 200);
        break;
      case 'technical':
        performance = 1.1 - (stats.handling / 200);
        break;
      default:
        const avgPerf = (stats.top_speed + stats.acceleration + stats.handling) / 3;
        performance = 1.1 - (avgPerf / 200);
    }
    
    return Math.max(0.8, Math.min(1.2, performance));
  }

  // Apply strategy card effects
  applyStrategyCards(strategyCards, stage) {
    let timeMultiplier = 1.0;
    let crashMultiplier = 1.0;
    
    for (const card of strategyCards) {
      const stats = card.stats || {};
      const abilities = card.abilities || {};
      
      // Apply stat bonuses
      if (stats.speed_bonus) {
        timeMultiplier *= (1 - stats.speed_bonus / 100);
      }
      if (stats.speed_penalty) {
        timeMultiplier *= (1 - stats.speed_penalty / 100);
      }
      if (stats.crash_risk) {
        crashMultiplier *= (1 + stats.crash_risk / 100);
      }
      
      // Weather-specific effects
      if (abilities.rain_master && stage.weather === 'rain') {
        timeMultiplier *= (1 - abilities.rain_master / 100);
      }
      if (abilities.snow_master && stage.weather === 'snow') {
        timeMultiplier *= (1 - abilities.snow_master / 100);
      }
    }
    
    return { timeMultiplier, crashMultiplier };
  }

  // Calculate risk effect
  calculateRiskEffect(riskLevel) {
    switch (riskLevel) {
      case 'conservative':
        return { timeMultiplier: 1.05, crashMultiplier: 0.5 };
      case 'aggressive':
        return { timeMultiplier: 0.95, crashMultiplier: 2.0 };
      default: // balanced
        return { timeMultiplier: 1.0, crashMultiplier: 1.0 };
    }
  }

  // Calculate crash chance
  calculateCrashChance(driverStats, vehicleStats, stage, riskLevel, weatherEffect, surfaceEffect) {
    let baseCrashChance = 0.03; // 3% base chance
    
    // Driver skill reduces crash chance
    const handlingFactor = driverStats.stats.handling / 100;
    baseCrashChance *= (1.5 - handlingFactor);
    
    // Vehicle reliability reduces crash chance
    const reliabilityFactor = vehicleStats.stats.reliability / 100;
    baseCrashChance *= (1.2 - reliabilityFactor);
    
    // Stage difficulty increases crash chance
    baseCrashChance *= (1 + (stage.difficulty - 1) * 0.2);
    
    // Weather and surface effects
    baseCrashChance *= weatherEffect.crashMultiplier;
    baseCrashChance *= surfaceEffect.crashMultiplier;
    
    // Risk level effect
    const riskEffect = this.calculateRiskEffect(riskLevel);
    baseCrashChance *= riskEffect.crashMultiplier;
    
    return Math.max(0.001, Math.min(0.25, baseCrashChance));
  }

  // Get effective stats with level bonuses
  getEffectiveStats(cardData) {
    const baseStats = cardData.stats || {};
    const level = cardData.level || 1;
    const abilities = cardData.abilities || {};
    
    // Apply level bonus (5% per level above 1)
    const levelBonus = (level - 1) * 0.05;
    const effectiveStats = {};
    
    for (const [stat, value] of Object.entries(baseStats)) {
      if (typeof value === 'number') {
        effectiveStats[stat] = Math.round(value * (1 + levelBonus));
      } else {
        effectiveStats[stat] = value;
      }
    }
    
    return {
      stats: effectiveStats,
      abilities,
      level
    };
  }

  // Calculate final position based on performance
  calculateFinalPosition(totalTime, crashed, mechanicalFailures) {
    // Base position calculation (simplified)
    let position = Math.max(1, Math.min(50, Math.round(totalTime / 10000)));
    
    // Penalties for crashes and failures
    if (crashed) position += randomBetween(5, 15);
    position += mechanicalFailures * randomBetween(3, 8);
    
    // Add some randomness for AI competitors
    position += randomBetween(-2, 5);
    
    return Math.max(1, Math.min(50, position));
  }

  // Calculate stage position
  calculateStagePosition(stageTime, difficulty) {
    // Simplified stage position calculation
    const basePosition = Math.round(stageTime / 5000) + randomBetween(-2, 3);
    return Math.max(1, Math.min(30, basePosition));
  }

  // Calculate rewards based on final position
  calculateRewards(finalPosition, rallyRewards) {
    const championshipPoints = RALLY_REWARDS.CHAMPIONSHIP_POINTS[finalPosition] || 0;
    const baseCoins = RALLY_REWARDS.BASE_COINS[finalPosition] || 10;
    
    return {
      championshipPoints,
      coins: baseCoins,
      xp: rallyRewards.xp || 50,
      cardPack: finalPosition <= 3 ? rallyRewards.card_pack : null,
      gems: finalPosition === 1 ? (rallyRewards.gems || 0) : 0
    };
  }

  // Generate stage notes
  generateStageNotes(crashed, mechanicalFailure, stagePosition) {
    if (mechanicalFailure) {
      return "Mechanical failure cost valuable time!";
    }
    if (crashed) {
      return "Off the road! Lost time recovering.";
    }
    if (stagePosition <= 3) {
      return "Excellent stage time!";
    }
    if (stagePosition <= 10) {
      return "Good pace maintained.";
    }
    return "Steady progress through the stage.";
  }

  // Generate rally summary
  generateRallySummary(stageResults, finalPosition) {
    const totalStages = stageResults.length;
    const crashes = stageResults.filter(s => s.crashed).length;
    const mechanicalFailures = stageResults.filter(s => s.mechanicalFailure).length;
    const topStageFinishes = stageResults.filter(s => s.stagePosition <= 5).length;
    
    let summary = `Finished ${finalPosition}${this.getOrdinalSuffix(finalPosition)} overall in ${totalStages} stages.`;
    
    if (topStageFinishes > totalStages / 2) {
      summary += " Consistently fast throughout the rally!";
    }
    
    if (crashes > 0) {
      summary += ` ${crashes} crash${crashes > 1 ? 'es' : ''} affected the result.`;
    }
    
    if (mechanicalFailures > 0) {
      summary += ` ${mechanicalFailures} mechanical issue${mechanicalFailures > 1 ? 's' : ''} caused delays.`;
    }
    
    return summary;
  }

  // Helper to get ordinal suffix
  getOrdinalSuffix(num) {
    const j = num % 10;
    const k = num % 100;
    if (j === 1 && k !== 11) return "st";
    if (j === 2 && k !== 12) return "nd";
    if (j === 3 && k !== 13) return "rd";
    return "th";
  }
}

module.exports = new RallySimulator();
