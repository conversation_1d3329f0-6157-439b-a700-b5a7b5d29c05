// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

User _$UserFromJson(Map<String, dynamic> json) => User(
      id: json['id'] as int,
      username: json['username'] as String,
      email: json['email'] as String,
      coins: json['coins'] as int,
      gems: json['gems'] as int,
      level: json['level'] as int,
      xp: json['xp'] as int,
      createdAt: json['createdAt'] == null
          ? null
          : DateTime.parse(json['createdAt'] as String),
      lastLogin: json['lastLogin'] == null
          ? null
          : DateTime.parse(json['lastLogin'] as String),
    );

Map<String, dynamic> _$UserToJson(User instance) => <String, dynamic>{
      'id': instance.id,
      'username': instance.username,
      'email': instance.email,
      'coins': instance.coins,
      'gems': instance.gems,
      'level': instance.level,
      'xp': instance.xp,
      'createdAt': instance.createdAt?.toIso8601String(),
      'lastLogin': instance.lastLogin?.toIso8601String(),
    };

UserStats _$UserStatsFromJson(Map<String, dynamic> json) => UserStats(
      rallyStats: RallyStats.fromJson(json['rallyStats'] as Map<String, dynamic>),
      cardStats: CardStats.fromJson(json['cardStats'] as Map<String, dynamic>),
      level: json['level'] as int,
      xp: json['xp'] as int,
      xpToNext: json['xpToNext'] as int,
    );

Map<String, dynamic> _$UserStatsToJson(UserStats instance) => <String, dynamic>{
      'rallyStats': instance.rallyStats,
      'cardStats': instance.cardStats,
      'level': instance.level,
      'xp': instance.xp,
      'xpToNext': instance.xpToNext,
    };

RallyStats _$RallyStatsFromJson(Map<String, dynamic> json) => RallyStats(
      totalRallies: json['total_rallies'] as int,
      avgPosition: (json['avg_position'] as num?)?.toDouble(),
      bestPosition: json['best_position'] as int?,
      totalPoints: json['total_points'] as int,
      bestTime: json['best_time'] as int?,
    );

Map<String, dynamic> _$RallyStatsToJson(RallyStats instance) => <String, dynamic>{
      'total_rallies': instance.totalRallies,
      'avg_position': instance.avgPosition,
      'best_position': instance.bestPosition,
      'total_points': instance.totalPoints,
      'best_time': instance.bestTime,
    };

CardStats _$CardStatsFromJson(Map<String, dynamic> json) => CardStats(
      totalCards: json['total_cards'] as int,
      legendaryCards: json['legendary_cards'] as int,
      epicCards: json['epic_cards'] as int,
      rareCards: json['rare_cards'] as int,
      commonCards: json['common_cards'] as int,
    );

Map<String, dynamic> _$CardStatsToJson(CardStats instance) => <String, dynamic>{
      'total_cards': instance.totalCards,
      'legendary_cards': instance.legendaryCards,
      'epic_cards': instance.epicCards,
      'rare_cards': instance.rareCards,
      'common_cards': instance.commonCards,
    };
