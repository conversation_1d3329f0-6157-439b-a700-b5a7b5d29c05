class AppConstants {
  // App Information
  static const String appName = 'Rally Championship Manager';
  static const String appVersion = '1.0.0';
  static const String appDescription = 'Collectible card game focused on rally racing';
  
  // API Configuration
  static const String baseUrl = 'http://localhost:3003/api';
  static const Duration apiTimeout = Duration(seconds: 30);
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String settingsKey = 'app_settings';
  
  // Game Constants
  static const int maxDeckSize = 20;
  static const int minDeckSize = 15;
  static const int minStrategyCards = 3;
  static const int maxStrategyCards = 5;
  static const int maxDailyRallies = 10;
  
  // Card Rarities
  static const List<String> cardRarities = [
    'common',
    'rare', 
    'epic',
    'legendary'
  ];
  
  // Card Types
  static const List<String> cardTypes = [
    'driver',
    'codriver',
    'vehicle',
    'strategy'
  ];
  
  // Championship Tiers
  static const List<String> championshipTiers = [
    'bronze',
    'silver',
    'gold',
    'elite'
  ];
  
  // Risk Levels
  static const List<String> riskLevels = [
    'conservative',
    'balanced',
    'aggressive'
  ];
  
  // Weather Conditions
  static const List<String> weatherConditions = [
    'clear',
    'rain',
    'snow',
    'fog'
  ];
  
  // Surface Types
  static const List<String> surfaceTypes = [
    'tarmac',
    'gravel',
    'snow',
    'mixed'
  ];
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 800);
  static const Duration cardFlipAnimation = Duration(milliseconds: 600);
  static const Duration packOpenAnimation = Duration(milliseconds: 1200);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double cardAspectRatio = 0.7; // Width / Height
  static const double borderRadius = 12.0;
  static const double smallBorderRadius = 8.0;
  static const double largeBorderRadius = 20.0;
  
  // Colors (will be used in theme)
  static const int primaryColorValue = 0xFF1E88E5; // Blue
  static const int accentColorValue = 0xFFFF6B35; // Orange
  static const int successColorValue = 0xFF4CAF50; // Green
  static const int warningColorValue = 0xFFFF9800; // Amber
  static const int errorColorValue = 0xFFF44336; // Red
  
  // Rarity Colors
  static const Map<String, int> rarityColors = {
    'common': 0xFF9E9E9E, // Grey
    'rare': 0xFF2196F3, // Blue
    'epic': 0xFF9C27B0, // Purple
    'legendary': 0xFFFF9800, // Orange/Gold
  };
  
  // Achievement Types
  static const List<String> achievementTypes = [
    'rally',
    'collection',
    'progression',
    'special'
  ];
  
  // Pack Types
  static const List<String> packTypes = [
    'bronze',
    'silver',
    'gold',
    'legendary'
  ];
  
  // Sound Effects
  static const String cardFlipSound = 'sounds/card_flip.mp3';
  static const String packOpenSound = 'sounds/pack_open.mp3';
  static const String buttonClickSound = 'sounds/button_click.mp3';
  static const String achievementSound = 'sounds/achievement.mp3';
  static const String rallyStartSound = 'sounds/rally_start.mp3';
  static const String rallyFinishSound = 'sounds/rally_finish.mp3';
  static const String coinSound = 'sounds/coin.mp3';
  static const String gemSound = 'sounds/gem.mp3';
  
  // Images
  static const String logoImage = 'images/logo.png';
  static const String splashBackground = 'images/splash_background.jpg';
  static const String cardBackImage = 'images/card_back.png';
  static const String packBronzeImage = 'images/pack_bronze.png';
  static const String packSilverImage = 'images/pack_silver.png';
  static const String packGoldImage = 'images/pack_gold.png';
  static const String packLegendaryImage = 'images/pack_legendary.png';
  
  // Animations
  static const String loadingAnimation = 'animations/loading.json';
  static const String successAnimation = 'animations/success.json';
  static const String packOpenAnimationFile = 'animations/pack_open.json';
  static const String achievementAnimation = 'animations/achievement.json';
  
  // Error Messages
  static const String networkError = 'Network connection error. Please check your internet connection.';
  static const String serverError = 'Server error. Please try again later.';
  static const String unknownError = 'An unknown error occurred. Please try again.';
  static const String invalidCredentials = 'Invalid email or password.';
  static const String insufficientFunds = 'Insufficient coins or gems.';
  static const String invalidDeck = 'Invalid deck configuration.';
  
  // Success Messages
  static const String loginSuccess = 'Welcome back!';
  static const String registerSuccess = 'Account created successfully!';
  static const String purchaseSuccess = 'Purchase completed successfully!';
  static const String upgradeSuccess = 'Card upgraded successfully!';
  static const String achievementUnlocked = 'Achievement unlocked!';
  
  // Validation
  static const int minUsernameLength = 3;
  static const int maxUsernameLength = 20;
  static const int minPasswordLength = 6;
  static const int maxPasswordLength = 100;
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache
  static const Duration cacheExpiry = Duration(minutes: 5);
  static const int maxCacheSize = 100;
}

// Enum-like classes for type safety
class CardRarity {
  static const String common = 'common';
  static const String rare = 'rare';
  static const String epic = 'epic';
  static const String legendary = 'legendary';
}

class CardType {
  static const String driver = 'driver';
  static const String codriver = 'codriver';
  static const String vehicle = 'vehicle';
  static const String strategy = 'strategy';
}

class ChampionshipTier {
  static const String bronze = 'bronze';
  static const String silver = 'silver';
  static const String gold = 'gold';
  static const String elite = 'elite';
}

class RiskLevel {
  static const String conservative = 'conservative';
  static const String balanced = 'balanced';
  static const String aggressive = 'aggressive';
}
