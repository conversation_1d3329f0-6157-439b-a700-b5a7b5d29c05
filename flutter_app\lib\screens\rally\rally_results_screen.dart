import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/card.dart';
import '../../providers/deck_provider.dart';
import '../../widgets/custom_button.dart';

class RallyResultsScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> rallyInfo;
  final Deck deck;
  final RallyResult result;

  const RallyResultsScreen({
    super.key,
    required this.rallyInfo,
    required this.deck,
    required this.result,
  });

  @override
  ConsumerState<RallyResultsScreen> createState() => _RallyResultsScreenState();
}

class _RallyResultsScreenState extends ConsumerState<RallyResultsScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: SafeArea(
                child: Padding(
                  padding: const EdgeInsets.all(AppConstants.defaultPadding),
                  child: Column(
                    children: [
                      // Header
                      _buildHeader(),
                      
                      const SizedBox(height: 32),
                      
                      // Position display
                      _buildPositionDisplay(),
                      
                      const SizedBox(height: 32),
                      
                      // Results details
                      _buildResultsDetails(),
                      
                      const SizedBox(height: 24),
                      
                      // Rewards
                      _buildRewards(),
                      
                      const Spacer(),
                      
                      // Actions
                      _buildActions(),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      children: [
        Text(
          'Rally Complete!',
          style: AppTheme.headingLarge.copyWith(
            color: Colors.white,
            fontSize: 32,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          widget.rallyInfo['name'] ?? 'Rally Event',
          style: AppTheme.headingMedium.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildPositionDisplay() {
    final isWinner = widget.result.position == 1;
    final isPodium = widget.result.position <= 3;
    
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: isPodium
            ? [AppTheme.successColor, AppTheme.warningColor]
            : [AppTheme.primaryColor, AppTheme.accentColor],
        ),
        boxShadow: [
          BoxShadow(
            color: (isPodium ? AppTheme.successColor : AppTheme.primaryColor).withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 5,
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (isWinner) ...[
            const Icon(
              Icons.emoji_events,
              color: Colors.white,
              size: 48,
            ),
            const SizedBox(height: 8),
          ],
          Text(
            '${widget.result.position}${widget.result.positionSuffix}',
            style: AppTheme.headingLarge.copyWith(
              color: Colors.white,
              fontSize: 48,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            'PLACE',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white,
              letterSpacing: 2.0,
            ),
          ),
        ],
      ),
    ).animate().scale(
      begin: const Offset(0.5, 0.5),
      end: const Offset(1.0, 1.0),
      duration: 600.ms,
      curve: Curves.elasticOut,
    );
  }

  Widget _buildResultsDetails() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(
        children: [
          Text(
            'Race Details',
            style: AppTheme.headingSmall.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildDetailItem(
                'Time',
                widget.result.formattedTime,
                Icons.timer,
              ),
              _buildDetailItem(
                'Participants',
                '${widget.result.totalParticipants}',
                Icons.people,
              ),
              _buildDetailItem(
                'Stages',
                '${widget.rallyInfo['stages'] ?? 5}',
                Icons.flag,
              ),
            ],
          ),
          if (widget.result.isPersonalBest) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                border: Border.all(color: AppTheme.successColor.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(
                    Icons.star,
                    color: AppTheme.successColor,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Personal Best Time!',
                    style: AppTheme.bodyMedium.copyWith(
                      color: AppTheme.successColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 24,
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.bodyLarge.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildRewards() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: AppTheme.warningColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            'Rewards Earned',
            style: AppTheme.headingSmall.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildRewardItem(
                'Coins',
                '+${widget.result.coinsEarned}',
                Icons.monetization_on,
                AppTheme.warningColor,
              ),
              _buildRewardItem(
                'XP',
                '+${widget.result.xpEarned}',
                Icons.trending_up,
                AppTheme.primaryColor,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRewardItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            shape: BoxShape.circle,
            border: Border.all(color: color.withOpacity(0.3)),
          ),
          child: Icon(
            icon,
            color: color,
            size: 28,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          value,
          style: AppTheme.bodyLarge.copyWith(
            color: color,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildActions() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: PrimaryButton(
            text: 'Race Again',
            onPressed: () {
              // Navigate back to rally selection
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            icon: Icons.refresh,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: 'Share Result',
                onPressed: _shareResult,
                icon: Icons.share,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: SecondaryButton(
                text: 'View Deck',
                onPressed: _viewDeck,
                icon: Icons.style,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: TextButton(
            onPressed: () {
              Navigator.of(context).popUntil((route) => route.isFirst);
            },
            child: Text(
              'Back to Home',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white70,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _shareResult() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Share functionality coming soon!'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  void _viewDeck() {
    showDialog(
      context: context,
      builder: (context) => _DeckViewDialog(deck: widget.deck),
    );
  }
}

class _DeckViewDialog extends StatelessWidget {
  final Deck deck;

  const _DeckViewDialog({required this.deck});

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: AppTheme.darkSurface,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: Text(
                    deck.name,
                    style: AppTheme.headingSmall.copyWith(color: Colors.white),
                  ),
                ),
                IconButton(
                  onPressed: () => Navigator.of(context).pop(),
                  icon: const Icon(Icons.close, color: Colors.white70),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Deck used in this rally',
              style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
            ),
            const SizedBox(height: 16),
            // TODO: Show deck cards here
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppTheme.darkCard,
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              ),
              child: Column(
                children: [
                  _buildDeckSlot('Driver', deck.driverCardId),
                  const SizedBox(height: 8),
                  _buildDeckSlot('Co-driver', deck.codriverCardId),
                  const SizedBox(height: 8),
                  _buildDeckSlot('Vehicle', deck.vehicleCardId),
                  const SizedBox(height: 8),
                  _buildDeckSlot('Strategy Cards', deck.strategyCards.length),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeckSlot(String label, dynamic value) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
        ),
        Text(
          value.toString(),
          style: AppTheme.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}
