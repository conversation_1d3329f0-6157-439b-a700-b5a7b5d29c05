import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/card.dart';
import '../../providers/shop_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/card_widget.dart';

class DailyBonusDialog extends ConsumerStatefulWidget {
  const DailyBonusDialog({super.key});

  @override
  ConsumerState<DailyBonusDialog> createState() => _DailyBonusDialogState();
}

class _DailyBonusDialogState extends ConsumerState<DailyBonusDialog>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late AnimationController _cardController;
  late ConfettiController _confettiController;

  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _cardRevealAnimation;

  bool _isClaiming = false;
  bool _hasRevealed = false;
  GameCard? _bonusCard;
  int _bonusCoins = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _generateDailyBonus();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _cardController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 2),
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _cardRevealAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _cardController,
      curve: Curves.easeOutBack,
    ));

    _animationController.forward();
  }

  void _generateDailyBonus() {
    // Generate a random daily bonus card and coins
    final rarities = ['common', 'common', 'common', 'rare', 'rare', 'epic'];
    final types = ['driver', 'codriver', 'vehicle', 'strategy'];
    final names = [
      'Rally Rookie', 'Speed Demon', 'Corner Master', 'Drift King',
      'Mountain Climber', 'Desert Racer', 'Forest Runner', 'City Slicker'
    ];

    final randomRarity = rarities[DateTime.now().millisecond % rarities.length];
    final randomType = types[DateTime.now().millisecond % types.length];
    final randomName = names[DateTime.now().millisecond % names.length];

    _bonusCard = GameCard(
      id: DateTime.now().millisecond,
      name: randomName,
      type: randomType,
      rarity: randomRarity,
      stats: {
        'speed': 60 + (DateTime.now().millisecond % 40),
        'handling': 60 + (DateTime.now().millisecond % 40),
        'acceleration': 60 + (DateTime.now().millisecond % 40),
      },
      description: 'Daily bonus card',
    );

    // Bonus coins based on rarity
    switch (randomRarity) {
      case 'common':
        _bonusCoins = 50;
        break;
      case 'rare':
        _bonusCoins = 100;
        break;
      case 'epic':
        _bonusCoins = 200;
        break;
      case 'legendary':
        _bonusCoins = 500;
        break;
      default:
        _bonusCoins = 50;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _cardController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Stack(
        children: [
          AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: _scaleAnimation.value,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Container(
                    constraints: const BoxConstraints(maxWidth: 400),
                    decoration: BoxDecoration(
                      color: AppTheme.darkSurface,
                      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.5),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        // Header
                        _buildHeader(),
                        
                        // Content
                        _buildContent(),
                        
                        // Actions
                        _buildActions(),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
          
          // Confetti
          Positioned.fill(
            child: ConfettiWidget(
              confettiController: _confettiController,
              blastDirectionality: BlastDirectionality.explosive,
              shouldLoop: false,
              colors: const [
                AppTheme.primaryColor,
                AppTheme.accentColor,
                AppTheme.successColor,
                AppTheme.warningColor,
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          colors: [AppTheme.primaryColor, AppTheme.accentColor],
        ),
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.card_giftcard,
            color: Colors.white,
            size: 32,
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Daily Bonus',
                  style: AppTheme.headingSmall.copyWith(
                    color: Colors.white,
                  ),
                ),
                Text(
                  'Your free daily reward is ready!',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (!_hasRevealed) ...[
            // Mystery card
            _buildMysteryCard(),
            const SizedBox(height: 16),
            Text(
              'Tap to reveal your daily bonus!',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
          ] else ...[
            // Revealed content
            _buildRevealedContent(),
          ],
        ],
      ),
    );
  }

  Widget _buildMysteryCard() {
    return GestureDetector(
      onTap: _hasRevealed ? null : _revealBonus,
      child: Container(
        width: 160,
        height: 220,
        decoration: BoxDecoration(
          gradient: const LinearGradient(
            colors: [AppTheme.primaryColor, AppTheme.accentColor],
          ),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppTheme.primaryColor.withOpacity(0.3),
              blurRadius: 15,
              spreadRadius: 2,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.help_outline,
              size: 60,
              color: Colors.white,
            ),
            const SizedBox(height: 16),
            Text(
              'MYSTERY',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.white,
              ),
            ),
            Text(
              'BONUS',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white70,
              ),
            ),
          ],
        ),
      ).animate(onPlay: (controller) => controller.repeat())
        .shimmer(duration: 2000.ms, color: Colors.white.withOpacity(0.3)),
    );
  }

  Widget _buildRevealedContent() {
    return AnimatedBuilder(
      animation: _cardController,
      builder: (context, child) {
        return Column(
          children: [
            // Bonus card
            Transform.scale(
              scale: _cardRevealAnimation.value,
              child: _bonusCard != null
                ? CardWidget(
                    gameCard: _bonusCard!,
                    showLevel: false,
                    showFavorite: false,
                    width: 160,
                  )
                : const SizedBox.shrink(),
            ),
            
            const SizedBox(height: 20),
            
            // Bonus coins
            FadeTransition(
              opacity: _cardRevealAnimation,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppTheme.darkCard,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(
                    color: AppTheme.warningColor.withOpacity(0.3),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const Icon(
                      Icons.monetization_on,
                      color: AppTheme.warningColor,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      '+$_bonusCoins Coins',
                      style: AppTheme.headingSmall.copyWith(
                        color: AppTheme.warningColor,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Congratulations message
            FadeTransition(
              opacity: _cardRevealAnimation,
              child: Text(
                'Congratulations! Come back tomorrow for another bonus!',
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.white70,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildActions() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          if (!_hasRevealed) ...[
            SizedBox(
              width: double.infinity,
              child: PrimaryButton(
                text: _isClaiming ? 'Revealing...' : 'Claim Bonus',
                onPressed: _isClaiming ? null : _revealBonus,
                isLoading: _isClaiming,
              ),
            ),
          ] else ...[
            SizedBox(
              width: double.infinity,
              child: PrimaryButton(
                text: 'Collect Rewards',
                onPressed: _collectRewards,
              ),
            ),
          ],
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: SecondaryButton(
              text: 'Close',
              onPressed: () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _revealBonus() async {
    if (_isClaiming || _hasRevealed) return;

    setState(() {
      _isClaiming = true;
    });

    // Simulate API call
    await Future.delayed(const Duration(milliseconds: 500));

    setState(() {
      _hasRevealed = true;
      _isClaiming = false;
    });

    // Start card reveal animation
    _cardController.forward();
    
    // Start confetti
    _confettiController.play();

    // Mark daily bonus as claimed
    ref.read(dailyBonusProvider.notifier).claimDailyBonus();
  }

  void _collectRewards() {
    // Update user coins
    final user = ref.read(currentUserProvider);
    if (user != null) {
      ref.read(authProvider.notifier).updateCoins(user.coins + _bonusCoins);
    }

    // Show success message
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Daily bonus collected! +$_bonusCoins coins'),
        backgroundColor: AppTheme.successColor,
      ),
    );

    Navigator.of(context).pop();
  }
}
