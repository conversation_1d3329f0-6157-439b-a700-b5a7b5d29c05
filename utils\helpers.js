const { PACK_PROBABILITIES, CARD_RARITIES, XP_REQUIREMENTS } = require('./constants');

// Generate random number between min and max (inclusive)
const randomBetween = (min, max) => {
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

// Generate random float between min and max
const randomFloat = (min, max) => {
  return Math.random() * (max - min) + min;
};

// Select random item from array
const randomChoice = (array) => {
  return array[Math.floor(Math.random() * array.length)];
};

// Weighted random selection based on probabilities
const weightedRandom = (probabilities) => {
  const totalWeight = Object.values(probabilities).reduce((sum, weight) => sum + weight, 0);
  let random = Math.random() * totalWeight;
  
  for (const [item, weight] of Object.entries(probabilities)) {
    random -= weight;
    if (random <= 0) {
      return item;
    }
  }
  
  // Fallback to first item if something goes wrong
  return Object.keys(probabilities)[0];
};

// Determine card rarity based on pack type
const determineCardRarity = (packType) => {
  const probabilities = PACK_PROBABILITIES[packType.toUpperCase()];
  if (!probabilities) {
    return CARD_RARITIES.COMMON;
  }
  return weightedRandom(probabilities);
};

// Calculate XP required for next level
const getXPForLevel = (level) => {
  if (level <= 10) {
    return XP_REQUIREMENTS[level] || 0;
  }
  
  // For levels above 10, use formula: previous + (level * 50)
  let totalXP = XP_REQUIREMENTS[10];
  for (let i = 11; i <= level; i++) {
    totalXP += i * 50;
  }
  return totalXP;
};

// Calculate level from total XP
const getLevelFromXP = (totalXP) => {
  let level = 1;
  while (getXPForLevel(level + 1) <= totalXP) {
    level++;
  }
  return level;
};

// Calculate XP needed for next level
const getXPToNextLevel = (currentXP) => {
  const currentLevel = getLevelFromXP(currentXP);
  const nextLevelXP = getXPForLevel(currentLevel + 1);
  return nextLevelXP - currentXP;
};

// Format time in milliseconds to readable format
const formatTime = (milliseconds) => {
  const totalSeconds = Math.floor(milliseconds / 1000);
  const minutes = Math.floor(totalSeconds / 60);
  const seconds = totalSeconds % 60;
  const ms = milliseconds % 1000;
  
  if (minutes > 0) {
    return `${minutes}:${seconds.toString().padStart(2, '0')}.${ms.toString().padStart(3, '0')}`;
  } else {
    return `${seconds}.${ms.toString().padStart(3, '0')}`;
  }
};

// Parse time string to milliseconds
const parseTime = (timeString) => {
  const parts = timeString.split(':');
  let totalMs = 0;
  
  if (parts.length === 2) {
    // Format: "MM:SS.mmm"
    const minutes = parseInt(parts[0]);
    const secondsParts = parts[1].split('.');
    const seconds = parseInt(secondsParts[0]);
    const milliseconds = parseInt(secondsParts[1] || '0');
    
    totalMs = (minutes * 60 * 1000) + (seconds * 1000) + milliseconds;
  } else {
    // Format: "SS.mmm"
    const secondsParts = parts[0].split('.');
    const seconds = parseInt(secondsParts[0]);
    const milliseconds = parseInt(secondsParts[1] || '0');
    
    totalMs = (seconds * 1000) + milliseconds;
  }
  
  return totalMs;
};

// Calculate stage time based on various factors
const calculateStageTime = (baseTime, driverStats, vehicleStats, conditions, riskLevel) => {
  let timeModifier = 1.0;
  
  // Driver skill impact (higher stats = faster times)
  const avgDriverStat = (driverStats.speed + driverStats.handling + driverStats.consistency) / 3;
  timeModifier *= (1 - (avgDriverStat - 50) / 200); // 50 is baseline, max 25% improvement
  
  // Vehicle performance impact
  const avgVehicleStat = (vehicleStats.topSpeed + vehicleStats.acceleration + vehicleStats.handling) / 3;
  timeModifier *= (1 - (avgVehicleStat - 50) / 250); // Max 20% improvement
  
  // Weather conditions impact
  if (conditions.weather === 'rain') {
    timeModifier *= 1.15; // 15% slower in rain
  } else if (conditions.weather === 'snow') {
    timeModifier *= 1.25; // 25% slower in snow
  }
  
  // Risk level impact
  switch (riskLevel) {
    case 'conservative':
      timeModifier *= 1.05; // 5% slower but safer
      break;
    case 'aggressive':
      timeModifier *= 0.95; // 5% faster but riskier
      break;
    // 'balanced' has no modifier
  }
  
  // Add some randomness (±2%)
  const randomFactor = randomFloat(0.98, 1.02);
  timeModifier *= randomFactor;
  
  return Math.round(baseTime * timeModifier);
};

// Calculate crash probability
const calculateCrashProbability = (driverStats, vehicleStats, conditions, riskLevel) => {
  let baseCrashChance = 0.05; // 5% base crash chance
  
  // Driver handling reduces crash chance
  baseCrashChance *= (1 - (driverStats.handling - 50) / 200);
  
  // Vehicle reliability reduces crash chance
  baseCrashChance *= (1 - (vehicleStats.reliability - 50) / 300);
  
  // Weather increases crash chance
  if (conditions.weather === 'rain') {
    baseCrashChance *= 1.5;
  } else if (conditions.weather === 'snow') {
    baseCrashChance *= 2.0;
  }
  
  // Risk level affects crash chance
  switch (riskLevel) {
    case 'conservative':
      baseCrashChance *= 0.5; // Half the crash chance
      break;
    case 'aggressive':
      baseCrashChance *= 2.0; // Double the crash chance
      break;
  }
  
  // Ensure crash chance stays within reasonable bounds
  return Math.max(0.001, Math.min(0.3, baseCrashChance));
};

// Sanitize user input
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return input;
  return input.trim().replace(/[<>]/g, '');
};

// Generate unique rally event name
const generateRallyName = () => {
  const locations = ['Alpine', 'Forest', 'Desert', 'Mountain', 'Coastal', 'Arctic', 'Jungle', 'Canyon'];
  const descriptors = ['Classic', 'Championship', 'Grand Prix', 'Trophy', 'Challenge', 'Masters', 'Elite', 'Pro'];
  
  const location = randomChoice(locations);
  const descriptor = randomChoice(descriptors);
  
  return `${location} ${descriptor}`;
};

module.exports = {
  randomBetween,
  randomFloat,
  randomChoice,
  weightedRandom,
  determineCardRarity,
  getXPForLevel,
  getLevelFromXP,
  getXPToNextLevel,
  formatTime,
  parseTime,
  calculateStageTime,
  calculateCrashProbability,
  sanitizeInput,
  generateRallyName
};
