-- Shop Items for Rally Championship Manager

-- Card Packs
INSERT INTO shop_items (name, type, price_coins, price_gems, price_real_money, contents) VALUES
('Bronze Pack', 'card_pack', 500, 0, 0.99,
 '{"cards": 3, "guaranteed_rarity": "common", "probabilities": {"common": 70, "rare": 25, "epic": 5, "legendary": 0}}'),

('Silver Pack', 'card_pack', 0, 50, 2.99,
 '{"cards": 5, "guaranteed_rarity": "rare", "probabilities": {"common": 50, "rare": 35, "epic": 12, "legendary": 3}}'),

('Gold Pack', 'card_pack', 0, 100, 4.99,
 '{"cards": 8, "guaranteed_rarity": "epic", "probabilities": {"common": 30, "rare": 40, "epic": 25, "legendary": 5}}'),

('Legendary Pack', 'card_pack', 0, 200, 9.99,
 '{"cards": 5, "guaranteed_rarity": "legendary", "probabilities": {"common": 0, "rare": 40, "epic": 40, "legendary": 20}}'),

('Starter Pack', 'card_pack', 100, 0, 0,
 '{"cards": 5, "guaranteed_rarity": "common", "probabilities": {"common": 80, "rare": 20, "epic": 0, "legendary": 0}}');

-- Currency Packages
INSERT INTO shop_items (name, type, price_coins, price_gems, price_real_money, contents) VALUES
('Small Coin Package', 'currency', 0, 0, 0.99,
 '{"coins": 1000}'),

('Medium Coin Package', 'currency', 0, 0, 3.99,
 '{"coins": 5000, "bonus_coins": 500}'),

('Large Coin Package', 'currency', 0, 0, 9.99,
 '{"coins": 15000, "bonus_coins": 2000}'),

('Small Gem Package', 'currency', 0, 0, 0.99,
 '{"gems": 100}'),

('Medium Gem Package', 'currency', 0, 0, 3.99,
 '{"gems": 500, "bonus_gems": 50}'),

('Large Gem Package', 'currency', 0, 0, 9.99,
 '{"gems": 1500, "bonus_gems": 200}');

-- Special Offers (Limited Time)
INSERT INTO shop_items (name, type, price_coins, price_gems, price_real_money, contents, limited_quantity, available_until) VALUES
('Welcome Bundle', 'card_pack', 0, 0, 4.99,
 '{"cards": 10, "coins": 2000, "gems": 200, "guaranteed_legendary": 1}',
 1, NOW() + INTERVAL '30 days'),

('Championship Starter', 'card_pack', 0, 0, 19.99,
 '{"driver_cards": 3, "codriver_cards": 2, "vehicle_cards": 3, "strategy_cards": 5, "coins": 5000, "gems": 500}',
 1, NOW() + INTERVAL '7 days');

-- Daily/Weekly Offers
INSERT INTO shop_items (name, type, price_coins, price_gems, price_real_money, contents) VALUES
('Daily Bronze Pack', 'card_pack', 300, 0, 0,
 '{"cards": 3, "guaranteed_rarity": "common", "probabilities": {"common": 70, "rare": 25, "epic": 5, "legendary": 0}}'),

('Weekly Silver Pack', 'card_pack', 0, 30, 0,
 '{"cards": 5, "guaranteed_rarity": "rare", "probabilities": {"common": 50, "rare": 35, "epic": 12, "legendary": 3}}');
