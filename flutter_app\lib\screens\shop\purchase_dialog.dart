import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/shop.dart';
import '../../models/card.dart';
import '../../providers/auth_provider.dart';
import '../../providers/shop_provider.dart';
import '../../widgets/custom_button.dart';
import '../../widgets/pack_opening_animation.dart';

class PurchaseDialog extends ConsumerStatefulWidget {
  final ShopItem item;

  const PurchaseDialog({
    super.key,
    required this.item,
  });

  @override
  ConsumerState<PurchaseDialog> createState() => _PurchaseDialogState();
}

class _PurchaseDialogState extends ConsumerState<PurchaseDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  String _selectedPaymentMethod = 'coins';
  bool _isPurchasing = false;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _selectDefaultPaymentMethod();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutBack,
    ));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _animationController.forward();
  }

  void _selectDefaultPaymentMethod() {
    if (widget.item.canBuyWithCoins) {
      _selectedPaymentMethod = 'coins';
    } else if (widget.item.canBuyWithGems) {
      _selectedPaymentMethod = 'gems';
    } else if (widget.item.canBuyWithMoney) {
      _selectedPaymentMethod = 'money';
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    return Dialog(
      backgroundColor: Colors.transparent,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Container(
                constraints: const BoxConstraints(maxWidth: 400),
                decoration: BoxDecoration(
                  color: AppTheme.darkSurface,
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.5),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    _buildHeader(),
                    
                    // Item details
                    _buildItemDetails(),
                    
                    // Payment methods
                    _buildPaymentMethods(user),
                    
                    // Actions
                    _buildActions(user),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            _getItemColor().withOpacity(0.3),
            _getItemColor().withOpacity(0.1),
          ],
        ),
        borderRadius: const BorderRadius.vertical(
          top: Radius.circular(AppConstants.borderRadius),
        ),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: _getItemColor().withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: _getItemColor().withOpacity(0.3),
              ),
            ),
            child: Icon(
              _getItemIcon(),
              color: _getItemColor(),
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.name,
                  style: AppTheme.headingSmall.copyWith(
                    color: Colors.white,
                  ),
                ),
                if (widget.item.description != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    widget.item.description!,
                    style: AppTheme.bodySmall.copyWith(
                      color: Colors.white70,
                    ),
                  ),
                ],
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(
              Icons.close,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItemDetails() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Item Details',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.darkCard,
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
            ),
            child: Column(
              children: [
                if (widget.item.isCardPack) ...[
                  _buildDetailRow('Cards', '${widget.item.cardCount}'),
                  _buildDetailRow('Pack Type', widget.item.packType?.toUpperCase() ?? 'MIXED'),
                ],
                if (widget.item.isCurrency) ...[
                  if (widget.item.contents['coins'] != null)
                    _buildDetailRow('Coins', '${widget.item.contents['coins']}'),
                  if (widget.item.contents['gems'] != null)
                    _buildDetailRow('Gems', '${widget.item.contents['gems']}'),
                ],
                if (widget.item.discountPercentage != null)
                  _buildDetailRow('Discount', '${widget.item.discountPercentage}% OFF', 
                    valueColor: AppTheme.successColor),
                if (widget.item.limitedQuantity != null)
                  _buildDetailRow('Limited', '${widget.item.limitedQuantity} remaining',
                    valueColor: AppTheme.errorColor),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, {Color? valueColor}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white70,
            ),
          ),
          Text(
            value,
            style: AppTheme.bodySmall.copyWith(
              color: valueColor ?? Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethods(user) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Payment Method',
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Column(
            children: [
              if (widget.item.canBuyWithCoins)
                _buildPaymentOption(
                  'coins',
                  Icons.monetization_on,
                  'Coins',
                  widget.item.discountedCoinsPrice.toString(),
                  AppTheme.warningColor,
                  user?.coins ?? 0 >= widget.item.discountedCoinsPrice,
                ),
              if (widget.item.canBuyWithGems)
                _buildPaymentOption(
                  'gems',
                  Icons.diamond,
                  'Gems',
                  widget.item.discountedGemsPrice.toString(),
                  AppTheme.primaryColor,
                  user?.gems ?? 0 >= widget.item.discountedGemsPrice,
                ),
              if (widget.item.canBuyWithMoney)
                _buildPaymentOption(
                  'money',
                  Icons.attach_money,
                  'Real Money',
                  '\$${widget.item.discountedRealMoneyPrice.toStringAsFixed(2)}',
                  AppTheme.successColor,
                  true, // Always available for real money
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentOption(
    String method,
    IconData icon,
    String label,
    String price,
    Color color,
    bool canAfford,
  ) {
    final isSelected = _selectedPaymentMethod == method;
    
    return GestureDetector(
      onTap: canAfford ? () {
        setState(() {
          _selectedPaymentMethod = method;
        });
      } : null,
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected ? color.withOpacity(0.2) : AppTheme.darkCard,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: isSelected ? color : Colors.white12,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Icon(
              icon,
              color: canAfford ? color : Colors.white38,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                label,
                style: AppTheme.bodyMedium.copyWith(
                  color: canAfford ? Colors.white : Colors.white38,
                  fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
                ),
              ),
            ),
            Text(
              price,
              style: AppTheme.bodyMedium.copyWith(
                color: canAfford ? color : Colors.white38,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: color,
                size: 20,
              ),
            if (!canAfford)
              const Icon(
                Icons.block,
                color: Colors.white38,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActions(user) {
    final canPurchase = _canAffordSelectedMethod(user);
    
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          SizedBox(
            width: double.infinity,
            child: PrimaryButton(
              text: _isPurchasing ? 'Processing...' : 'Purchase',
              onPressed: canPurchase && !_isPurchasing ? _handlePurchase : null,
              isLoading: _isPurchasing,
            ),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: SecondaryButton(
              text: 'Cancel',
              onPressed: _isPurchasing ? null : () => Navigator.of(context).pop(),
            ),
          ),
        ],
      ),
    );
  }

  bool _canAffordSelectedMethod(user) {
    switch (_selectedPaymentMethod) {
      case 'coins':
        return (user?.coins ?? 0) >= widget.item.discountedCoinsPrice;
      case 'gems':
        return (user?.gems ?? 0) >= widget.item.discountedGemsPrice;
      case 'money':
        return true; // Always available for real money
      default:
        return false;
    }
  }

  Future<void> _handlePurchase() async {
    setState(() {
      _isPurchasing = true;
    });

    try {
      // Simulate purchase process
      await Future.delayed(const Duration(seconds: 1));

      if (widget.item.isCardPack) {
        // Show pack opening animation
        _showPackOpening();
      } else {
        // Show success message
        _showPurchaseSuccess();
      }

      // Update user currency
      final user = ref.read(currentUserProvider);
      if (user != null) {
        int newCoins = user.coins;
        int newGems = user.gems;

        switch (_selectedPaymentMethod) {
          case 'coins':
            newCoins -= widget.item.discountedCoinsPrice;
            break;
          case 'gems':
            newGems -= widget.item.discountedGemsPrice;
            break;
        }

        // Add purchased currency
        if (widget.item.contents['coins'] != null) {
          newCoins += widget.item.contents['coins'] as int;
        }
        if (widget.item.contents['gems'] != null) {
          newGems += widget.item.contents['gems'] as int;
        }

        ref.read(authProvider.notifier).updateCoins(newCoins);
        ref.read(authProvider.notifier).updateGems(newGems);
      }

    } catch (e) {
      _showPurchaseError(e.toString());
    } finally {
      setState(() {
        _isPurchasing = false;
      });
    }
  }

  void _showPackOpening() {
    Navigator.of(context).pop(); // Close purchase dialog
    
    // Create mock pack result
    final packResult = _createMockPackResult();
    
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PackOpeningAnimation(
          packResult: packResult,
          onComplete: () {
            // Handle pack opening completion
          },
        ),
        fullscreenDialog: true,
      ),
    );
  }

  PackOpeningResult _createMockPackResult() {
    // Create mock cards based on pack type
    final cards = <GameCard>[];
    final packType = widget.item.packType ?? 'bronze';
    
    for (int i = 0; i < widget.item.cardCount; i++) {
      cards.add(GameCard(
        id: i + 1,
        name: 'Mock Card ${i + 1}',
        type: ['driver', 'codriver', 'vehicle', 'strategy'][i % 4],
        rarity: _getRandomRarity(packType),
        stats: {'speed': 75, 'handling': 80, 'acceleration': 70},
      ));
    }

    return PackOpeningResult(
      cards: cards,
      packValue: widget.item.discountedCoinsPrice,
      packStats: PackStats(
        total: cards.length,
        common: cards.where((c) => c.rarity == 'common').length,
        rare: cards.where((c) => c.rarity == 'rare').length,
        epic: cards.where((c) => c.rarity == 'epic').length,
        legendary: cards.where((c) => c.rarity == 'legendary').length,
        byType: {
          'driver': cards.where((c) => c.type == 'driver').length,
          'codriver': cards.where((c) => c.type == 'codriver').length,
          'vehicle': cards.where((c) => c.type == 'vehicle').length,
          'strategy': cards.where((c) => c.type == 'strategy').length,
        },
      ),
      packType: packType,
    );
  }

  String _getRandomRarity(String packType) {
    switch (packType) {
      case 'bronze':
        return ['common', 'common', 'rare'][DateTime.now().millisecond % 3];
      case 'silver':
        return ['rare', 'rare', 'epic'][DateTime.now().millisecond % 3];
      case 'gold':
        return ['epic', 'epic', 'legendary'][DateTime.now().millisecond % 3];
      case 'legendary':
        return 'legendary';
      default:
        return 'common';
    }
  }

  void _showPurchaseSuccess() {
    Navigator.of(context).pop(); // Close purchase dialog
    
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Purchase successful!'),
        backgroundColor: AppTheme.successColor,
      ),
    );
  }

  void _showPurchaseError(String error) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Purchase failed: $error'),
        backgroundColor: AppTheme.errorColor,
      ),
    );
  }

  Color _getItemColor() {
    if (widget.item.isCardPack) {
      final packType = widget.item.packType;
      if (packType != null) {
        return AppTheme.getRarityColor(packType);
      }
    }
    
    if (widget.item.isCurrency) {
      if (widget.item.name.toLowerCase().contains('gem')) {
        return AppTheme.primaryColor;
      }
      return AppTheme.warningColor;
    }
    
    return AppTheme.primaryColor;
  }

  IconData _getItemIcon() {
    if (widget.item.isCardPack) {
      return Icons.card_giftcard;
    }
    
    if (widget.item.isCurrency) {
      if (widget.item.name.toLowerCase().contains('gem')) {
        return Icons.diamond;
      }
      return Icons.monetization_on;
    }
    
    return Icons.shopping_bag;
  }
}
