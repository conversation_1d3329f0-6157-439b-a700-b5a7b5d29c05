const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const achievementService = require('../services/achievementService');

const router = express.Router();

// Get player's achievement progress
router.get('/', authenticateToken, async (req, res) => {
  try {
    const achievements = await achievementService.getPlayerAchievementProgress(req.user.id);
    
    res.json({ achievements });
  } catch (error) {
    console.error('Get achievements error:', error);
    res.status(500).json({ error: 'Failed to fetch achievements' });
  }
});

// Get unclaimed achievements
router.get('/unclaimed', authenticateToken, async (req, res) => {
  try {
    const unclaimedAchievements = await achievementService.getUnclaimedAchievements(req.user.id);
    
    res.json({ achievements: unclaimedAchievements });
  } catch (error) {
    console.error('Get unclaimed achievements error:', error);
    res.status(500).json({ error: 'Failed to fetch unclaimed achievements' });
  }
});

// Claim achievement rewards
router.post('/claim/:achievementId', authenticateToken, async (req, res) => {
  try {
    const { achievementId } = req.params;

    const result = await achievementService.claimAchievement(req.user.id, parseInt(achievementId));

    res.json({
      success: true,
      achievement: result.achievement,
      rewards: result.rewards
    });

  } catch (error) {
    console.error('Claim achievement error:', error);
    res.status(400).json({ error: error.message || 'Failed to claim achievement' });
  }
});

// Check for new completed achievements
router.post('/check', authenticateToken, async (req, res) => {
  try {
    const completedAchievements = await achievementService.checkPlayerAchievements(req.user.id);

    res.json({
      newCompletions: completedAchievements,
      count: completedAchievements.length
    });

  } catch (error) {
    console.error('Check achievements error:', error);
    res.status(500).json({ error: 'Failed to check achievements' });
  }
});

// Get achievement statistics (admin/debug)
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await achievementService.getAchievementStats();
    
    res.json({ stats });
  } catch (error) {
    console.error('Get achievement stats error:', error);
    res.status(500).json({ error: 'Failed to fetch achievement statistics' });
  }
});

module.exports = router;
