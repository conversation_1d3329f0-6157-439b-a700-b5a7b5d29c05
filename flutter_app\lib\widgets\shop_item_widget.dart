import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../models/shop.dart';

class ShopItemWidget extends StatefulWidget {
  final ShopItem item;
  final VoidCallback? onTap;
  final bool showDiscount;

  const ShopItemWidget({
    super.key,
    required this.item,
    this.onTap,
    this.showDiscount = true,
  });

  @override
  State<ShopItemWidget> createState() => _ShopItemWidgetState();
}

class _ShopItemWidgetState extends State<ShopItemWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: _getItemColor().withOpacity(0.2),
                    blurRadius: 8 + (_glowAnimation.value * 4),
                    spreadRadius: 1 + (_glowAnimation.value * 2),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: Stack(
                  children: [
                    // Background
                    _buildBackground(),
                    
                    // Content
                    _buildContent(),
                    
                    // Discount badge
                    if (widget.showDiscount && widget.item.discountPercentage != null)
                      _buildDiscountBadge(),
                    
                    // Limited badge
                    if (widget.item.limitedQuantity != null)
                      _buildLimitedBadge(),
                    
                    // Unavailable overlay
                    if (!widget.item.isAvailable)
                      _buildUnavailableOverlay(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.darkCard,
            AppTheme.darkCard.withOpacity(0.8),
            _getItemColor().withOpacity(0.1),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(12.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Item image/icon
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: _getItemColor().withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getItemColor().withOpacity(0.3),
                ),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _getItemIcon(),
                      size: 32,
                      color: _getItemColor(),
                    ),
                    if (widget.item.isCardPack) ...[
                      const SizedBox(height: 4),
                      Text(
                        '${widget.item.cardCount} Cards',
                        style: AppTheme.bodySmall.copyWith(
                          color: _getItemColor(),
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Item name and description
          Expanded(
            flex: 2,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.item.name,
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (widget.item.description != null) ...[
                  const SizedBox(height: 2),
                  Text(
                    widget.item.description!,
                    style: AppTheme.bodySmall.copyWith(
                      color: Colors.white54,
                      fontSize: 10,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const Spacer(),
                _buildPricing(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPricing() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Coins price
        if (widget.item.canBuyWithCoins) ...[
          Row(
            children: [
              const Icon(
                Icons.monetization_on,
                color: AppTheme.warningColor,
                size: 14,
              ),
              const SizedBox(width: 4),
              if (widget.item.discountPercentage != null) ...[
                Text(
                  widget.item.priceCoins.toString(),
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white54,
                    fontSize: 10,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                widget.item.discountedCoinsPrice.toString(),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
        
        // Gems price
        if (widget.item.canBuyWithGems) ...[
          const SizedBox(height: 2),
          Row(
            children: [
              const Icon(
                Icons.diamond,
                color: AppTheme.primaryColor,
                size: 14,
              ),
              const SizedBox(width: 4),
              if (widget.item.discountPercentage != null) ...[
                Text(
                  widget.item.priceGems.toString(),
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white54,
                    fontSize: 10,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                widget.item.discountedGemsPrice.toString(),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
        
        // Real money price
        if (widget.item.canBuyWithMoney) ...[
          const SizedBox(height: 2),
          Row(
            children: [
              const Icon(
                Icons.attach_money,
                color: AppTheme.successColor,
                size: 14,
              ),
              if (widget.item.discountPercentage != null) ...[
                Text(
                  '\$${widget.item.priceRealMoney.toStringAsFixed(2)}',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white54,
                    fontSize: 10,
                    decoration: TextDecoration.lineThrough,
                  ),
                ),
                const SizedBox(width: 4),
              ],
              Text(
                '\$${widget.item.discountedRealMoneyPrice.toStringAsFixed(2)}',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.w600,
                  fontSize: 11,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildDiscountBadge() {
    return Positioned(
      top: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppTheme.errorColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          '-${widget.item.discountPercentage}%',
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildLimitedBadge() {
    return Positioned(
      top: 8,
      left: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppTheme.accentColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'LIMITED',
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white,
            fontSize: 9,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildUnavailableOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.7),
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        ),
        child: const Center(
          child: Text(
            'SOLD OUT',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
              fontSize: 12,
            ),
          ),
        ),
      ),
    );
  }

  Color _getItemColor() {
    if (widget.item.isCardPack) {
      final packType = widget.item.packType;
      if (packType != null) {
        return AppTheme.getRarityColor(packType);
      }
    }
    
    if (widget.item.isCurrency) {
      if (widget.item.name.toLowerCase().contains('gem')) {
        return AppTheme.primaryColor;
      }
      return AppTheme.warningColor;
    }
    
    return AppTheme.primaryColor;
  }

  IconData _getItemIcon() {
    if (widget.item.isCardPack) {
      return Icons.card_giftcard;
    }
    
    if (widget.item.isCurrency) {
      if (widget.item.name.toLowerCase().contains('gem')) {
        return Icons.diamond;
      }
      return Icons.monetization_on;
    }
    
    return Icons.shopping_bag;
  }
}

// Compact shop item widget for lists
class CompactShopItemWidget extends StatelessWidget {
  final ShopItem item;
  final VoidCallback? onTap;

  const CompactShopItemWidget({
    super.key,
    required this.item,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.darkCard,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: _getItemColor().withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            // Item icon
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                color: _getItemColor().withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getItemColor().withOpacity(0.3),
                ),
              ),
              child: Icon(
                _getItemIcon(),
                color: _getItemColor(),
                size: 24,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Item info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          item.name,
                          style: AppTheme.bodyMedium.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.w600,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      if (item.discountPercentage != null)
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: AppTheme.errorColor,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            '-${item.discountPercentage}%',
                            style: AppTheme.bodySmall.copyWith(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  _buildCompactPricing(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompactPricing() {
    return Wrap(
      spacing: 12,
      children: [
        if (item.canBuyWithCoins)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.monetization_on,
                color: AppTheme.warningColor,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                item.discountedCoinsPrice.toString(),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.warningColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        if (item.canBuyWithGems)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.diamond,
                color: AppTheme.primaryColor,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                item.discountedGemsPrice.toString(),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        if (item.canBuyWithMoney)
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.attach_money,
                color: AppTheme.successColor,
                size: 14,
              ),
              Text(
                item.discountedRealMoneyPrice.toStringAsFixed(2),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
      ],
    );
  }

  Color _getItemColor() {
    if (item.isCardPack) {
      final packType = item.packType;
      if (packType != null) {
        return AppTheme.getRarityColor(packType);
      }
    }
    
    if (item.isCurrency) {
      if (item.name.toLowerCase().contains('gem')) {
        return AppTheme.primaryColor;
      }
      return AppTheme.warningColor;
    }
    
    return AppTheme.primaryColor;
  }

  IconData _getItemIcon() {
    if (item.isCardPack) {
      return Icons.card_giftcard;
    }
    
    if (item.isCurrency) {
      if (item.name.toLowerCase().contains('gem')) {
        return Icons.diamond;
      }
      return Icons.monetization_on;
    }
    
    return Icons.shopping_bag;
  }
}
