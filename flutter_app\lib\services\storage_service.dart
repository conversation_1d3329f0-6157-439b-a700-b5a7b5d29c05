import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/app_constants.dart';
import '../models/user.dart';

class StorageService {
  static final StorageService _instance = StorageService._internal();
  static StorageService get instance => _instance;
  StorageService._internal();

  late SharedPreferences _prefs;
  late FlutterSecureStorage _secureStorage;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _secureStorage = const FlutterSecureStorage(
      aOptions: AndroidOptions(
        encryptedSharedPreferences: true,
      ),
      iOptions: IOSOptions(
        accessibility: KeychainItemAccessibility.first_unlock_this_device,
      ),
    );
  }

  // Token management (secure storage)
  Future<void> saveTokens(String accessToken, String refreshToken) async {
    await _secureStorage.write(
      key: AppConstants.accessTokenKey,
      value: accessToken,
    );
    await _secureStorage.write(
      key: AppConstants.refreshTokenKey,
      value: refreshToken,
    );
  }

  Future<String?> getAccessToken() async {
    return await _secureStorage.read(key: AppConstants.accessTokenKey);
  }

  Future<String?> getRefreshToken() async {
    return await _secureStorage.read(key: AppConstants.refreshTokenKey);
  }

  Future<void> clearTokens() async {
    await _secureStorage.delete(key: AppConstants.accessTokenKey);
    await _secureStorage.delete(key: AppConstants.refreshTokenKey);
  }

  // User data management (shared preferences)
  Future<void> saveUserData(User user) async {
    final userJson = jsonEncode(user.toJson());
    await _prefs.setString(AppConstants.userDataKey, userJson);
  }

  Future<User?> getUserData() async {
    final userJson = _prefs.getString(AppConstants.userDataKey);
    if (userJson != null) {
      try {
        final userMap = jsonDecode(userJson) as Map<String, dynamic>;
        return User.fromJson(userMap);
      } catch (e) {
        // If parsing fails, clear corrupted data
        await clearUserData();
      }
    }
    return null;
  }

  Future<void> clearUserData() async {
    await _prefs.remove(AppConstants.userDataKey);
  }

  // App settings
  Future<void> saveSettings(AppSettings settings) async {
    final settingsJson = jsonEncode(settings.toJson());
    await _prefs.setString(AppConstants.settingsKey, settingsJson);
  }

  Future<AppSettings> getSettings() async {
    final settingsJson = _prefs.getString(AppConstants.settingsKey);
    if (settingsJson != null) {
      try {
        final settingsMap = jsonDecode(settingsJson) as Map<String, dynamic>;
        return AppSettings.fromJson(settingsMap);
      } catch (e) {
        // Return default settings if parsing fails
        return AppSettings.defaultSettings();
      }
    }
    return AppSettings.defaultSettings();
  }

  // Cache management
  Future<void> cacheData(String key, Map<String, dynamic> data) async {
    final cacheData = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
    };
    await _prefs.setString('cache_$key', jsonEncode(cacheData));
  }

  Future<Map<String, dynamic>?> getCachedData(String key) async {
    final cachedJson = _prefs.getString('cache_$key');
    if (cachedJson != null) {
      try {
        final cacheData = jsonDecode(cachedJson) as Map<String, dynamic>;
        final timestamp = cacheData['timestamp'] as int;
        final cacheTime = DateTime.fromMillisecondsSinceEpoch(timestamp);
        
        // Check if cache is still valid
        if (DateTime.now().difference(cacheTime) < AppConstants.cacheExpiry) {
          return cacheData['data'] as Map<String, dynamic>;
        } else {
          // Cache expired, remove it
          await clearCache(key);
        }
      } catch (e) {
        // Clear corrupted cache
        await clearCache(key);
      }
    }
    return null;
  }

  Future<void> clearCache(String key) async {
    await _prefs.remove('cache_$key');
  }

  Future<void> clearAllCache() async {
    final keys = _prefs.getKeys().where((key) => key.startsWith('cache_'));
    for (final key in keys) {
      await _prefs.remove(key);
    }
  }

  // Game progress
  Future<void> saveGameProgress(String key, Map<String, dynamic> progress) async {
    await _prefs.setString('progress_$key', jsonEncode(progress));
  }

  Future<Map<String, dynamic>?> getGameProgress(String key) async {
    final progressJson = _prefs.getString('progress_$key');
    if (progressJson != null) {
      try {
        return jsonDecode(progressJson) as Map<String, dynamic>;
      } catch (e) {
        await _prefs.remove('progress_$key');
      }
    }
    return null;
  }

  // Tutorial and onboarding
  Future<void> setTutorialCompleted(String tutorialId) async {
    await _prefs.setBool('tutorial_$tutorialId', true);
  }

  Future<bool> isTutorialCompleted(String tutorialId) async {
    return _prefs.getBool('tutorial_$tutorialId') ?? false;
  }

  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    await _prefs.setBool('first_launch', isFirstLaunch);
  }

  Future<bool> isFirstLaunch() async {
    return _prefs.getBool('first_launch') ?? true;
  }

  // Statistics tracking
  Future<void> incrementStat(String statName) async {
    final currentValue = _prefs.getInt('stat_$statName') ?? 0;
    await _prefs.setInt('stat_$statName', currentValue + 1);
  }

  Future<int> getStat(String statName) async {
    return _prefs.getInt('stat_$statName') ?? 0;
  }

  Future<void> setStat(String statName, int value) async {
    await _prefs.setInt('stat_$statName', value);
  }

  // Clear all data
  Future<void> clearAll() async {
    await clearTokens();
    await clearUserData();
    await clearAllCache();
    await _prefs.clear();
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final accessToken = await getAccessToken();
    final userData = await getUserData();
    return accessToken != null && userData != null;
  }
}

// App settings model
class AppSettings {
  final bool soundEnabled;
  final bool musicEnabled;
  final bool notificationsEnabled;
  final bool hapticFeedbackEnabled;
  final double soundVolume;
  final double musicVolume;
  final String language;
  final bool darkMode;
  final bool autoPlay;
  final bool showTutorials;

  const AppSettings({
    required this.soundEnabled,
    required this.musicEnabled,
    required this.notificationsEnabled,
    required this.hapticFeedbackEnabled,
    required this.soundVolume,
    required this.musicVolume,
    required this.language,
    required this.darkMode,
    required this.autoPlay,
    required this.showTutorials,
  });

  factory AppSettings.defaultSettings() {
    return const AppSettings(
      soundEnabled: true,
      musicEnabled: true,
      notificationsEnabled: true,
      hapticFeedbackEnabled: true,
      soundVolume: 0.8,
      musicVolume: 0.6,
      language: 'en',
      darkMode: true,
      autoPlay: false,
      showTutorials: true,
    );
  }

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    return AppSettings(
      soundEnabled: json['soundEnabled'] ?? true,
      musicEnabled: json['musicEnabled'] ?? true,
      notificationsEnabled: json['notificationsEnabled'] ?? true,
      hapticFeedbackEnabled: json['hapticFeedbackEnabled'] ?? true,
      soundVolume: (json['soundVolume'] ?? 0.8).toDouble(),
      musicVolume: (json['musicVolume'] ?? 0.6).toDouble(),
      language: json['language'] ?? 'en',
      darkMode: json['darkMode'] ?? true,
      autoPlay: json['autoPlay'] ?? false,
      showTutorials: json['showTutorials'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'soundEnabled': soundEnabled,
      'musicEnabled': musicEnabled,
      'notificationsEnabled': notificationsEnabled,
      'hapticFeedbackEnabled': hapticFeedbackEnabled,
      'soundVolume': soundVolume,
      'musicVolume': musicVolume,
      'language': language,
      'darkMode': darkMode,
      'autoPlay': autoPlay,
      'showTutorials': showTutorials,
    };
  }

  AppSettings copyWith({
    bool? soundEnabled,
    bool? musicEnabled,
    bool? notificationsEnabled,
    bool? hapticFeedbackEnabled,
    double? soundVolume,
    double? musicVolume,
    String? language,
    bool? darkMode,
    bool? autoPlay,
    bool? showTutorials,
  }) {
    return AppSettings(
      soundEnabled: soundEnabled ?? this.soundEnabled,
      musicEnabled: musicEnabled ?? this.musicEnabled,
      notificationsEnabled: notificationsEnabled ?? this.notificationsEnabled,
      hapticFeedbackEnabled: hapticFeedbackEnabled ?? this.hapticFeedbackEnabled,
      soundVolume: soundVolume ?? this.soundVolume,
      musicVolume: musicVolume ?? this.musicVolume,
      language: language ?? this.language,
      darkMode: darkMode ?? this.darkMode,
      autoPlay: autoPlay ?? this.autoPlay,
      showTutorials: showTutorials ?? this.showTutorials,
    );
  }
}
