// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'shop.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

ShopItem _$ShopItemFromJson(Map<String, dynamic> json) => ShopItem(
      id: json['id'] as int,
      name: json['name'] as String,
      type: json['type'] as String,
      priceCoins: json['price_coins'] as int,
      priceGems: json['price_gems'] as int,
      priceRealMoney: (json['price_real_money'] as num).toDouble(),
      contents: json['contents'] as Map<String, dynamic>,
      description: json['description'] as String?,
      imageUrl: json['image_url'] as String?,
      isAvailable: json['is_available'] as bool? ?? true,
      discountPercentage: json['discount_percentage'] as int?,
      limitedQuantity: json['limited_quantity'] as int?,
      dailyLimit: json['daily_limit'] as int?,
    );

Map<String, dynamic> _$ShopItemToJson(ShopItem instance) => <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'type': instance.type,
      'price_coins': instance.priceCoins,
      'price_gems': instance.priceGems,
      'price_real_money': instance.priceRealMoney,
      'contents': instance.contents,
      'description': instance.description,
      'image_url': instance.imageUrl,
      'is_available': instance.isAvailable,
      'discount_percentage': instance.discountPercentage,
      'limited_quantity': instance.limitedQuantity,
      'daily_limit': instance.dailyLimit,
    };

Purchase _$PurchaseFromJson(Map<String, dynamic> json) => Purchase(
      id: json['id'] as int,
      playerId: json['player_id'] as int,
      itemId: json['item_id'] as int,
      paymentMethod: json['payment_method'] as String,
      amountPaid: (json['amount_paid'] as num).toDouble(),
      itemsReceived: json['items_received'] as Map<String, dynamic>,
      purchasedAt: DateTime.parse(json['purchased_at'] as String),
      item: json['item'] == null
          ? null
          : ShopItem.fromJson(json['item'] as Map<String, dynamic>),
    );

Map<String, dynamic> _$PurchaseToJson(Purchase instance) => <String, dynamic>{
      'id': instance.id,
      'player_id': instance.playerId,
      'item_id': instance.itemId,
      'payment_method': instance.paymentMethod,
      'amount_paid': instance.amountPaid,
      'items_received': instance.itemsReceived,
      'purchased_at': instance.purchasedAt.toIso8601String(),
      'item': instance.item,
    };

PackOpeningResult _$PackOpeningResultFromJson(Map<String, dynamic> json) =>
    PackOpeningResult(
      cards: (json['cards'] as List<dynamic>)
          .map((e) => GameCard.fromJson(e as Map<String, dynamic>))
          .toList(),
      packValue: json['pack_value'] as int,
      packStats: PackStats.fromJson(json['pack_stats'] as Map<String, dynamic>),
      packType: json['pack_type'] as String,
      duplicateInfo: (json['duplicate_info'] as List<dynamic>?)
          ?.map((e) => CardDuplicateInfo.fromJson(e as Map<String, dynamic>))
          .toList(),
    );

Map<String, dynamic> _$PackOpeningResultToJson(PackOpeningResult instance) =>
    <String, dynamic>{
      'cards': instance.cards,
      'pack_value': instance.packValue,
      'pack_stats': instance.packStats,
      'pack_type': instance.packType,
      'duplicate_info': instance.duplicateInfo,
    };

PackStats _$PackStatsFromJson(Map<String, dynamic> json) => PackStats(
      total: json['total'] as int,
      common: json['common'] as int,
      rare: json['rare'] as int,
      epic: json['epic'] as int,
      legendary: json['legendary'] as int,
      byType: Map<String, int>.from(json['by_type'] as Map),
    );

Map<String, dynamic> _$PackStatsToJson(PackStats instance) => <String, dynamic>{
      'total': instance.total,
      'common': instance.common,
      'rare': instance.rare,
      'epic': instance.epic,
      'legendary': instance.legendary,
      'by_type': instance.byType,
    };

CardDuplicateInfo _$CardDuplicateInfoFromJson(Map<String, dynamic> json) =>
    CardDuplicateInfo(
      cardId: json['card_id'] as int,
      isDuplicate: json['is_duplicate'] as bool,
      dustValue: json['dust_value'] as int,
    );

Map<String, dynamic> _$CardDuplicateInfoToJson(CardDuplicateInfo instance) =>
    <String, dynamic>{
      'card_id': instance.cardId,
      'is_duplicate': instance.isDuplicate,
      'dust_value': instance.dustValue,
    };

DailyBonus _$DailyBonusFromJson(Map<String, dynamic> json) => DailyBonus(
      bonusCard: GameCard.fromJson(json['bonus_card'] as Map<String, dynamic>),
      bonusCoins: json['bonus_coins'] as int,
      playerCoins: json['player_coins'] as int,
      isNew: json['is_new'] as bool,
    );

Map<String, dynamic> _$DailyBonusToJson(DailyBonus instance) =>
    <String, dynamic>{
      'bonus_card': instance.bonusCard,
      'bonus_coins': instance.bonusCoins,
      'player_coins': instance.playerCoins,
      'is_new': instance.isNew,
    };
