import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../widgets/custom_button.dart';
import 'deck_selection_screen.dart';

class RallyScreen extends ConsumerStatefulWidget {
  const RallyScreen({super.key});

  @override
  ConsumerState<RallyScreen> createState() => _RallyScreenState();
}

class _RallyScreenState extends ConsumerState<RallyScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Safe<PERSON>rea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Tab bar
            _buildTabBar(),
            
            // Content
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: const [
                  BronzeRalliesTab(),
                  SilverRalliesTab(),
                  GoldRalliesTab(),
                  EliteRalliesTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Rally Championships',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Choose your championship and race to victory',
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              // TODO: Show rally history
            },
            icon: const Icon(
              Icons.history,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppTheme.primaryColor,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.white54,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTheme.bodySmall,
        tabs: [
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppTheme.getTierColor('bronze'),
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text('BRONZE'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppTheme.getTierColor('silver'),
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text('SILVER'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppTheme.getTierColor('gold'),
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text('GOLD'),
              ],
            ),
          ),
          Tab(
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.emoji_events,
                  color: AppTheme.getTierColor('elite'),
                  size: 16,
                ),
                const SizedBox(width: 4),
                const Text('ELITE'),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// Rally tier tabs
class BronzeRalliesTab extends StatelessWidget {
  const BronzeRalliesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildRallyList('bronze', [
      _createMockRally('Forest Sprint', 3, 'Easy', 50, 0),
      _createMockRally('City Circuit', 4, 'Easy', 75, 0),
    ]);
  }
}

class SilverRalliesTab extends StatelessWidget {
  const SilverRalliesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildRallyList('silver', [
      _createMockRally('Mountain Challenge', 5, 'Medium', 100, 5),
      _createMockRally('Desert Storm', 5, 'Medium', 125, 5),
    ]);
  }
}

class GoldRalliesTab extends StatelessWidget {
  const GoldRalliesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildRallyList('gold', [
      _createMockRally('Arctic Expedition', 6, 'Hard', 200, 10),
      _createMockRally('Night Rally', 5, 'Hard', 175, 10),
    ]);
  }
}

class EliteRalliesTab extends StatelessWidget {
  const EliteRalliesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return _buildRallyList('elite', [
      _createMockRally('World Championship Final', 7, 'Expert', 500, 25),
    ]);
  }
}

Widget _buildRallyList(String tier, List<Map<String, dynamic>> rallies) {
  return Padding(
    padding: const EdgeInsets.all(AppConstants.defaultPadding),
    child: ListView.builder(
      itemCount: rallies.length,
      itemBuilder: (context, index) {
        final rally = rallies[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: _buildRallyCard(tier, rally),
        );
      },
    ),
  );
}

Widget _buildRallyCard(String tier, Map<String, dynamic> rally) {
  return Container(
    padding: const EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: AppTheme.darkCard,
      borderRadius: BorderRadius.circular(AppConstants.borderRadius),
      border: Border.all(
        color: AppTheme.getTierColor(tier).withOpacity(0.3),
      ),
      boxShadow: [
        BoxShadow(
          color: AppTheme.getTierColor(tier).withOpacity(0.1),
          blurRadius: 8,
          offset: const Offset(0, 2),
        ),
      ],
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    rally['name'],
                    style: AppTheme.headingSmall.copyWith(
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.emoji_events,
                        color: AppTheme.getTierColor(tier),
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        tier.toUpperCase(),
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.getTierColor(tier),
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: _getDifficultyColor(rally['difficulty']).withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                rally['difficulty'],
                style: AppTheme.bodySmall.copyWith(
                  color: _getDifficultyColor(rally['difficulty']),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Rally info
        Row(
          children: [
            _buildInfoItem(Icons.flag, '${rally['stages']} Stages'),
            const SizedBox(width: 16),
            _buildInfoItem(Icons.monetization_on, '${rally['coins']} coins'),
            if (rally['gems'] > 0) ...[
              const SizedBox(width: 16),
              _buildInfoItem(Icons.diamond, '${rally['gems']} gems'),
            ],
          ],
        ),
        
        const SizedBox(height: 16),
        
        // Action button
        SizedBox(
          width: double.infinity,
          child: PrimaryButton(
            text: 'Join Rally',
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => DeckSelectionScreen(
                    rallyInfo: {
                      'id': 'rally_${tier}_${rally['name'].toString().replaceAll(' ', '_').toLowerCase()}',
                      'name': rally['name'],
                      'tier': tier,
                      'stages': rally['stages'],
                      'difficulty': rally['difficulty'],
                      'coins': rally['coins'],
                      'gems': rally['gems'],
                    },
                  ),
                ),
              );
            },
          ),
        ),
      ],
    ),
  ).animate().fadeIn(duration: 300.ms).slideY(begin: 0.3, end: 0);
}

Widget _buildInfoItem(IconData icon, String text) {
  return Row(
    mainAxisSize: MainAxisSize.min,
    children: [
      Icon(
        icon,
        color: Colors.white54,
        size: 16,
      ),
      const SizedBox(width: 4),
      Text(
        text,
        style: AppTheme.bodySmall.copyWith(
          color: Colors.white70,
        ),
      ),
    ],
  );
}

Color _getDifficultyColor(String difficulty) {
  switch (difficulty.toLowerCase()) {
    case 'easy':
      return AppTheme.successColor;
    case 'medium':
      return AppTheme.warningColor;
    case 'hard':
      return AppTheme.errorColor;
    case 'expert':
      return AppTheme.accentColor;
    default:
      return Colors.white54;
  }
}

Map<String, dynamic> _createMockRally(String name, int stages, String difficulty, int coins, int gems) {
  return {
    'name': name,
    'stages': stages,
    'difficulty': difficulty,
    'coins': coins,
    'gems': gems,
  };
}
