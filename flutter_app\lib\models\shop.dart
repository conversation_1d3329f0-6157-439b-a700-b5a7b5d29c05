import 'package:json_annotation/json_annotation.dart';
import 'card.dart';

part 'shop.g.dart';

@JsonSerializable()
class ShopItem {
  final int id;
  final String name;
  final String type;
  @Json<PERSON>ey(name: 'price_coins')
  final int priceCoins;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'price_gems')
  final int priceGems;
  @<PERSON>son<PERSON>ey(name: 'price_real_money')
  final double priceRealMoney;
  final Map<String, dynamic> contents;
  final String? description;
  @<PERSON>son<PERSON>ey(name: 'image_url')
  final String? imageUrl;
  @Json<PERSON>ey(name: 'is_available')
  final bool isAvailable;
  @JsonKey(name: 'discount_percentage')
  final int? discountPercentage;
  @JsonKey(name: 'limited_quantity')
  final int? limitedQuantity;
  @Json<PERSON>ey(name: 'daily_limit')
  final int? dailyLimit;

  const ShopItem({
    required this.id,
    required this.name,
    required this.type,
    required this.priceCoins,
    required this.priceGems,
    required this.priceRealMoney,
    required this.contents,
    this.description,
    this.imageUrl,
    this.isAvailable = true,
    this.discountPercentage,
    this.limitedQuantity,
    this.dailyLimit,
  });

  factory ShopItem.fromJson(Map<String, dynamic> json) => _$ShopItemFromJson(json);
  Map<String, dynamic> toJson() => _$ShopItemToJson(this);

  // Check if item can be purchased with coins
  bool get canBuyWithCoins => priceCoins > 0;

  // Check if item can be purchased with gems
  bool get canBuyWithGems => priceGems > 0;

  // Check if item can be purchased with real money
  bool get canBuyWithMoney => priceRealMoney > 0;

  // Get discounted price for coins
  int get discountedCoinsPrice {
    if (discountPercentage == null) return priceCoins;
    return (priceCoins * (100 - discountPercentage!) / 100).round();
  }

  // Get discounted price for gems
  int get discountedGemsPrice {
    if (discountPercentage == null) return priceGems;
    return (priceGems * (100 - discountPercentage!) / 100).round();
  }

  // Get discounted price for real money
  double get discountedRealMoneyPrice {
    if (discountPercentage == null) return priceRealMoney;
    return priceRealMoney * (100 - discountPercentage!) / 100;
  }

  // Check if item is a card pack
  bool get isCardPack => type.toLowerCase().contains('pack');

  // Check if item is currency
  bool get isCurrency => type.toLowerCase().contains('currency') || type.toLowerCase().contains('coins') || type.toLowerCase().contains('gems');

  // Get pack type for card packs
  String? get packType {
    if (!isCardPack) return null;
    final lowerName = name.toLowerCase();
    if (lowerName.contains('bronze')) return 'bronze';
    if (lowerName.contains('silver')) return 'silver';
    if (lowerName.contains('gold')) return 'gold';
    if (lowerName.contains('legendary')) return 'legendary';
    return null;
  }

  // Get number of cards in pack
  int get cardCount {
    if (!isCardPack) return 0;
    return contents['cards'] as int? ?? 3;
  }

  @override
  String toString() {
    return 'ShopItem(id: $id, name: $name, type: $type)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is ShopItem && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

@JsonSerializable()
class Purchase {
  final int id;
  @JsonKey(name: 'player_id')
  final int playerId;
  @JsonKey(name: 'item_id')
  final int itemId;
  @JsonKey(name: 'payment_method')
  final String paymentMethod;
  @JsonKey(name: 'amount_paid')
  final double amountPaid;
  @JsonKey(name: 'items_received')
  final Map<String, dynamic> itemsReceived;
  @JsonKey(name: 'purchased_at')
  final DateTime purchasedAt;
  final ShopItem? item;

  const Purchase({
    required this.id,
    required this.playerId,
    required this.itemId,
    required this.paymentMethod,
    required this.amountPaid,
    required this.itemsReceived,
    required this.purchasedAt,
    this.item,
  });

  factory Purchase.fromJson(Map<String, dynamic> json) => _$PurchaseFromJson(json);
  Map<String, dynamic> toJson() => _$PurchaseToJson(this);

  // Get cards received from purchase
  List<GameCard> get cardsReceived {
    final cards = itemsReceived['cards'] as List?;
    if (cards == null) return [];
    return cards.map((cardJson) => GameCard.fromJson(cardJson)).toList();
  }

  // Get coins received from purchase
  int get coinsReceived {
    return itemsReceived['coins'] as int? ?? 0;
  }

  // Get gems received from purchase
  int get gemsReceived {
    return itemsReceived['gems'] as int? ?? 0;
  }

  @override
  String toString() {
    return 'Purchase(id: $id, itemId: $itemId, paymentMethod: $paymentMethod)';
  }
}

@JsonSerializable()
class PackOpeningResult {
  final List<GameCard> cards;
  @JsonKey(name: 'pack_value')
  final int packValue;
  @JsonKey(name: 'pack_stats')
  final PackStats packStats;
  @JsonKey(name: 'pack_type')
  final String packType;
  @JsonKey(name: 'duplicate_info')
  final List<CardDuplicateInfo>? duplicateInfo;

  const PackOpeningResult({
    required this.cards,
    required this.packValue,
    required this.packStats,
    required this.packType,
    this.duplicateInfo,
  });

  factory PackOpeningResult.fromJson(Map<String, dynamic> json) => _$PackOpeningResultFromJson(json);
  Map<String, dynamic> toJson() => _$PackOpeningResultToJson(this);

  // Check if pack contains any rare+ cards
  bool get hasRareOrBetter {
    return cards.any((card) => 
      card.rarity == 'rare' || 
      card.rarity == 'epic' || 
      card.rarity == 'legendary'
    );
  }

  // Check if pack contains legendary card
  bool get hasLegendary {
    return cards.any((card) => card.rarity == 'legendary');
  }

  // Get highest rarity in pack
  String get highestRarity {
    final rarityOrder = ['common', 'rare', 'epic', 'legendary'];
    String highest = 'common';
    
    for (final card in cards) {
      final currentIndex = rarityOrder.indexOf(card.rarity);
      final highestIndex = rarityOrder.indexOf(highest);
      if (currentIndex > highestIndex) {
        highest = card.rarity;
      }
    }
    
    return highest;
  }
}

@JsonSerializable()
class PackStats {
  final int total;
  final int common;
  final int rare;
  final int epic;
  final int legendary;
  @JsonKey(name: 'by_type')
  final Map<String, int> byType;

  const PackStats({
    required this.total,
    required this.common,
    required this.rare,
    required this.epic,
    required this.legendary,
    required this.byType,
  });

  factory PackStats.fromJson(Map<String, dynamic> json) => _$PackStatsFromJson(json);
  Map<String, dynamic> toJson() => _$PackStatsToJson(this);
}

@JsonSerializable()
class CardDuplicateInfo {
  @JsonKey(name: 'card_id')
  final int cardId;
  @JsonKey(name: 'is_duplicate')
  final bool isDuplicate;
  @JsonKey(name: 'dust_value')
  final int dustValue;

  const CardDuplicateInfo({
    required this.cardId,
    required this.isDuplicate,
    required this.dustValue,
  });

  factory CardDuplicateInfo.fromJson(Map<String, dynamic> json) => _$CardDuplicateInfoFromJson(json);
  Map<String, dynamic> toJson() => _$CardDuplicateInfoToJson(this);
}

@JsonSerializable()
class DailyBonus {
  @JsonKey(name: 'bonus_card')
  final GameCard bonusCard;
  @JsonKey(name: 'bonus_coins')
  final int bonusCoins;
  @JsonKey(name: 'player_coins')
  final int playerCoins;
  @JsonKey(name: 'is_new')
  final bool isNew;

  const DailyBonus({
    required this.bonusCard,
    required this.bonusCoins,
    required this.playerCoins,
    required this.isNew,
  });

  factory DailyBonus.fromJson(Map<String, dynamic> json) => _$DailyBonusFromJson(json);
  Map<String, dynamic> toJson() => _$DailyBonusToJson(this);
}
