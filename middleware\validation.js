const Joi = require('joi');

// Validation middleware factory
const validate = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.body);
    if (error) {
      return res.status(400).json({
        error: 'Validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

// Common validation schemas
const schemas = {
  // User registration
  register: Joi.object({
    username: Joi.string()
      .alphanum()
      .min(3)
      .max(20)
      .required()
      .messages({
        'string.alphanum': 'Username must contain only letters and numbers',
        'string.min': 'Username must be at least 3 characters long',
        'string.max': 'Username must be at most 20 characters long'
      }),
    email: Joi.string()
      .email()
      .required()
      .messages({
        'string.email': 'Please provide a valid email address'
      }),
    password: Joi.string()
      .min(6)
      .max(100)
      .required()
      .messages({
        'string.min': 'Password must be at least 6 characters long',
        'string.max': 'Password must be at most 100 characters long'
      })
  }),

  // User login
  login: Joi.object({
    email: <PERSON>i.string()
      .email()
      .required(),
    password: Joi.string()
      .required()
  }),

  // Rally deck creation
  createDeck: Joi.object({
    name: Joi.string()
      .min(1)
      .max(50)
      .required(),
    driverId: Joi.number()
      .integer()
      .positive()
      .required(),
    codriverId: Joi.number()
      .integer()
      .positive()
      .required(),
    vehicleId: Joi.number()
      .integer()
      .positive()
      .required(),
    strategyCards: Joi.array()
      .items(Joi.number().integer().positive())
      .min(3)
      .max(5)
      .required()
  }),

  // Rally participation
  joinRally: Joi.object({
    rallyId: Joi.number()
      .integer()
      .positive()
      .required(),
    deckId: Joi.number()
      .integer()
      .positive()
      .required()
  }),

  // Stage decision
  stageDecision: Joi.object({
    rallyResultId: Joi.number()
      .integer()
      .positive()
      .required(),
    stageIndex: Joi.number()
      .integer()
      .min(0)
      .required(),
    riskLevel: Joi.string()
      .valid('conservative', 'balanced', 'aggressive')
      .required(),
    strategyCardId: Joi.number()
      .integer()
      .positive()
      .allow(null)
  }),

  // Shop purchase
  purchase: Joi.object({
    itemId: Joi.number()
      .integer()
      .positive()
      .required(),
    quantity: Joi.number()
      .integer()
      .positive()
      .default(1)
  }),

  // Card upgrade
  upgradeCard: Joi.object({
    playerCardId: Joi.number()
      .integer()
      .positive()
      .required()
  })
};

// Query parameter validation
const validateQuery = (schema) => {
  return (req, res, next) => {
    const { error } = schema.validate(req.query);
    if (error) {
      return res.status(400).json({
        error: 'Query validation error',
        details: error.details.map(detail => ({
          field: detail.path.join('.'),
          message: detail.message
        }))
      });
    }
    next();
  };
};

// Common query schemas
const querySchemas = {
  pagination: Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
  }),

  leaderboard: Joi.object({
    tier: Joi.string().valid('bronze', 'silver', 'gold', 'elite').optional(),
    season: Joi.string().optional(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(50).default(20)
  })
};

module.exports = {
  validate,
  validateQuery,
  schemas,
  querySchemas
};
