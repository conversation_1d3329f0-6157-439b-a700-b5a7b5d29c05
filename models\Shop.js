const { query, beginTransaction, commitTransaction, rollbackTransaction } = require('../database/connection');
const { determineCardRarity, weightedRandom } = require('../utils/helpers');
const { Card, PlayerCard } = require('./Card');
const Player = require('./Player');

class Shop {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.type = data.type;
    this.priceCoins = data.price_coins;
    this.priceGems = data.price_gems;
    this.priceRealMoney = data.price_real_money;
    this.contents = data.contents;
    this.isActive = data.is_active;
    this.limitedQuantity = data.limited_quantity;
    this.availableUntil = data.available_until;
    this.createdAt = data.created_at;
  }

  // Find shop item by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM shop_items WHERE id = $1 AND is_active = true',
      [id]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Shop(result.rows[0]);
  }

  // Get all active shop items
  static async findAll() {
    const result = await query(`
      SELECT * FROM shop_items 
      WHERE is_active = true
      AND (available_until IS NULL OR available_until >= NOW())
      ORDER BY price_real_money ASC, price_gems ASC, price_coins ASC
    `);
    
    return result.rows.map(row => new Shop(row));
  }

  // Get shop items by type
  static async findByType(type) {
    const result = await query(
      'SELECT * FROM shop_items WHERE type = $1 AND is_active = true ORDER BY price_coins ASC, price_gems ASC',
      [type]
    );
    
    return result.rows.map(row => new Shop(row));
  }

  // Create new shop item
  static async create(itemData) {
    const { name, type, priceCoins, priceGems, priceRealMoney, contents, limitedQuantity, availableUntil } = itemData;
    
    const result = await query(
      `INSERT INTO shop_items (name, type, price_coins, price_gems, price_real_money, contents, limited_quantity, available_until) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [name, type, priceCoins, priceGems, priceRealMoney, JSON.stringify(contents), limitedQuantity, availableUntil]
    );
    
    return new Shop(result.rows[0]);
  }

  // Check if player can afford this item
  canPlayerAfford(playerCoins, playerGems) {
    return playerCoins >= this.priceCoins && playerGems >= this.priceGems;
  }

  // Check if item is available (not sold out, not expired)
  isAvailable() {
    // Check if expired
    if (this.availableUntil && new Date() > new Date(this.availableUntil)) {
      return false;
    }
    
    // Check if sold out (for limited items)
    if (this.limitedQuantity !== null) {
      // Would need to check purchase count, simplified for now
      return true;
    }
    
    return this.isActive;
  }

  // Purchase item for a player
  async purchase(playerId, paymentMethod = 'coins') {
    const client = await beginTransaction();
    
    try {
      // Get player
      const player = await Player.findById(playerId);
      if (!player) {
        throw new Error('Player not found');
      }

      // Check if item is available
      if (!this.isAvailable()) {
        throw new Error('Item is not available');
      }

      // Check if player can afford
      if (!this.canPlayerAfford(player.coins, player.gems)) {
        throw new Error('Insufficient funds');
      }

      // Deduct payment
      let success = false;
      if (paymentMethod === 'coins' && this.priceCoins > 0) {
        success = await player.spendCoins(this.priceCoins);
      } else if (paymentMethod === 'gems' && this.priceGems > 0) {
        success = await player.spendGems(this.priceGems);
      } else if (paymentMethod === 'real_money') {
        // Real money purchases would be handled by payment processor
        success = true;
      }

      if (!success) {
        throw new Error('Payment failed');
      }

      // Generate item contents
      const itemsReceived = await this.generateContents(playerId, client);

      // Record purchase
      const purchaseResult = await client.query(
        `INSERT INTO purchases (player_id, item_id, payment_method, amount_paid, items_received)
         VALUES ($1, $2, $3, $4, $5)
         RETURNING *`,
        [
          playerId,
          this.id,
          paymentMethod,
          paymentMethod === 'coins' ? this.priceCoins : this.priceGems,
          JSON.stringify(itemsReceived)
        ]
      );

      await commitTransaction(client);

      return {
        purchase: purchaseResult.rows[0],
        itemsReceived,
        playerCoins: player.coins,
        playerGems: player.gems
      };

    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Generate contents based on item type
  async generateContents(playerId, client) {
    const contents = this.contents;
    const itemsReceived = {
      coins: 0,
      gems: 0,
      cards: [],
      xp: 0
    };

    // Handle currency items
    if (contents.coins) {
      itemsReceived.coins += contents.coins;
      if (contents.bonus_coins) {
        itemsReceived.coins += contents.bonus_coins;
      }
      
      // Add coins to player
      await client.query(
        'UPDATE players SET coins = coins + $1 WHERE id = $2',
        [itemsReceived.coins, playerId]
      );
    }

    if (contents.gems) {
      itemsReceived.gems += contents.gems;
      if (contents.bonus_gems) {
        itemsReceived.gems += contents.bonus_gems;
      }
      
      // Add gems to player
      await client.query(
        'UPDATE players SET gems = gems + $1 WHERE id = $2',
        [itemsReceived.gems, playerId]
      );
    }

    // Handle card packs
    if (contents.cards) {
      const numCards = contents.cards;
      const probabilities = contents.probabilities || {};
      
      for (let i = 0; i < numCards; i++) {
        // Determine rarity
        let rarity;
        if (i === 0 && contents.guaranteed_rarity) {
          rarity = contents.guaranteed_rarity;
        } else {
          rarity = weightedRandom(probabilities);
        }

        // Get random card of that rarity
        const cardResult = await client.query(
          'SELECT * FROM card_definitions WHERE rarity = $1 AND is_active = true ORDER BY RANDOM() LIMIT 1',
          [rarity]
        );

        if (cardResult.rows.length > 0) {
          const card = cardResult.rows[0];
          
          // Add card to player's collection
          const playerCardResult = await client.query(
            'INSERT INTO player_cards (player_id, card_id) VALUES ($1, $2) RETURNING *',
            [playerId, card.id]
          );

          itemsReceived.cards.push({
            playerCardId: playerCardResult.rows[0].id,
            card: card,
            isNew: true
          });
        }
      }
    }

    // Handle guaranteed legendary
    if (contents.guaranteed_legendary) {
      const legendaryResult = await client.query(
        'SELECT * FROM card_definitions WHERE rarity = \'legendary\' AND is_active = true ORDER BY RANDOM() LIMIT 1'
      );

      if (legendaryResult.rows.length > 0) {
        const card = legendaryResult.rows[0];
        
        const playerCardResult = await client.query(
          'INSERT INTO player_cards (player_id, card_id) VALUES ($1, $2) RETURNING *',
          [playerId, card.id]
        );

        itemsReceived.cards.push({
          playerCardId: playerCardResult.rows[0].id,
          card: card,
          isNew: true,
          guaranteed: true
        });
      }
    }

    // Handle specific card types
    const cardTypes = ['driver_cards', 'codriver_cards', 'vehicle_cards', 'strategy_cards'];
    for (const cardType of cardTypes) {
      if (contents[cardType]) {
        const type = cardType.replace('_cards', '').replace('codriver', 'codriver');
        const numCards = contents[cardType];
        
        for (let i = 0; i < numCards; i++) {
          const cardResult = await client.query(
            'SELECT * FROM card_definitions WHERE type = $1 AND is_active = true ORDER BY RANDOM() LIMIT 1',
            [type]
          );

          if (cardResult.rows.length > 0) {
            const card = cardResult.rows[0];
            
            const playerCardResult = await client.query(
              'INSERT INTO player_cards (player_id, card_id) VALUES ($1, $2) RETURNING *',
              [playerId, card.id]
            );

            itemsReceived.cards.push({
              playerCardId: playerCardResult.rows[0].id,
              card: card,
              isNew: true
            });
          }
        }
      }
    }

    return itemsReceived;
  }

  // Get player's purchase history
  static async getPlayerPurchases(playerId, limit = 50) {
    const result = await query(`
      SELECT 
        p.*,
        si.name as item_name,
        si.type as item_type
      FROM purchases p
      JOIN shop_items si ON p.item_id = si.id
      WHERE p.player_id = $1
      ORDER BY p.purchased_at DESC
      LIMIT $2
    `, [playerId, limit]);

    return result.rows;
  }

  // Update shop item
  async update(updates) {
    const allowedFields = ['name', 'price_coins', 'price_gems', 'price_real_money', 'contents', 'is_active', 'limited_quantity', 'available_until'];
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field)) {
        updateFields.push(`${field} = $${paramCount}`);
        
        // JSON stringify contents
        if (field === 'contents') {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
        paramCount++;
      }
    }

    if (updateFields.length === 0) {
      return this;
    }

    values.push(this.id);
    const result = await query(
      `UPDATE shop_items SET ${updateFields.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length > 0) {
      Object.assign(this, result.rows[0]);
    }

    return this;
  }

  // Deactivate shop item
  async deactivate() {
    await this.update({ isActive: false });
    return this;
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      priceCoins: this.priceCoins,
      priceGems: this.priceGems,
      priceRealMoney: this.priceRealMoney,
      contents: this.contents,
      isActive: this.isActive,
      limitedQuantity: this.limitedQuantity,
      availableUntil: this.availableUntil,
      createdAt: this.createdAt,
      isAvailable: this.isAvailable()
    };
  }
}

module.exports = Shop;
