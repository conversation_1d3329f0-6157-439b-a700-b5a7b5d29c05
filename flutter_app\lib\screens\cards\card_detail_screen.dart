import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/card.dart';
import '../../providers/card_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';

class CardDetailScreen extends ConsumerStatefulWidget {
  final PlayerCard playerCard;

  const CardDetailScreen({
    super.key,
    required this.playerCard,
  });

  @override
  ConsumerState<CardDetailScreen> createState() => _CardDetailScreenState();
}

class _CardDetailScreenState extends ConsumerState<CardDetailScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOut,
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeOutCubic,
    ));

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final card = widget.playerCard.card;
    if (card == null) return const Scaffold();

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return FadeTransition(
            opacity: _fadeAnimation,
            child: SlideTransition(
              position: _slideAnimation,
              child: CustomScrollView(
                slivers: [
                  _buildAppBar(card),
                  SliverToBoxAdapter(
                    child: Padding(
                      padding: const EdgeInsets.all(AppConstants.defaultPadding),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildCardHeader(card),
                          const SizedBox(height: 24),
                          _buildStats(card),
                          const SizedBox(height: 24),
                          _buildAbilities(card),
                          const SizedBox(height: 24),
                          _buildLevelInfo(),
                          const SizedBox(height: 24),
                          _buildActions(),
                          const SizedBox(height: 32),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAppBar(GameCard card) {
    return SliverAppBar(
      expandedHeight: 300,
      pinned: true,
      backgroundColor: AppTheme.darkSurface,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppTheme.getRarityColor(card.rarity).withOpacity(0.3),
                AppTheme.darkBackground,
              ],
            ),
          ),
          child: Center(
            child: Hero(
              tag: 'card_${widget.playerCard.playerCardId}',
              child: Container(
                width: 200,
                height: 280,
                decoration: BoxDecoration(
                  color: AppTheme.getRarityColor(card.rarity).withOpacity(0.2),
                  borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                  border: Border.all(
                    color: AppTheme.getRarityColor(card.rarity),
                    width: 3,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppTheme.getRarityColor(card.rarity).withOpacity(0.5),
                      blurRadius: 20,
                      spreadRadius: 5,
                    ),
                  ],
                ),
                child: Center(
                  child: Icon(
                    _getCardTypeIcon(card.type),
                    size: 80,
                    color: AppTheme.getRarityColor(card.rarity),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          onPressed: () => _toggleFavorite(),
          icon: Icon(
            widget.playerCard.isFavorite ? Icons.favorite : Icons.favorite_border,
            color: widget.playerCard.isFavorite ? AppTheme.errorColor : Colors.white,
          ),
        ),
      ],
    );
  }

  Widget _buildCardHeader(GameCard card) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                card.name,
                style: AppTheme.headingMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.getRarityColor(card.rarity).withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: AppTheme.getRarityColor(card.rarity),
                ),
              ),
              child: Text(
                card.rarity.toUpperCase(),
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.getRarityColor(card.rarity),
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Text(
          card.type.toUpperCase(),
          style: AppTheme.bodyMedium.copyWith(
            color: Colors.white70,
            letterSpacing: 1.0,
          ),
        ),
        if (card.description != null) ...[
          const SizedBox(height: 16),
          Text(
            card.description!,
            style: AppTheme.bodyMedium.copyWith(
              color: Colors.white70,
            ),
          ),
        ],
        if (card.flavorText != null) ...[
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.darkCard.withOpacity(0.5),
              borderRadius: BorderRadius.circular(AppConstants.borderRadius),
              border: Border.all(
                color: Colors.white12,
              ),
            ),
            child: Text(
              card.flavorText!,
              style: AppTheme.bodySmall.copyWith(
                color: Colors.white54,
                fontStyle: FontStyle.italic,
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildStats(GameCard card) {
    final stats = card.primaryStats;
    if (stats.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Stats',
          style: AppTheme.headingSmall.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...stats.map((stat) => _buildStatBar(stat)),
      ],
    );
  }

  Widget _buildStatBar(CardStat stat) {
    final effectiveValue = widget.playerCard.getEffectiveStatValue(stat.name.toLowerCase().replaceAll(' ', '_'));
    final baseValue = stat.value;
    final hasBonus = effectiveValue > baseValue;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                stat.name,
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.white70,
                ),
              ),
              Row(
                children: [
                  if (hasBonus) ...[
                    Text(
                      baseValue.toString(),
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.white54,
                        decoration: TextDecoration.lineThrough,
                      ),
                    ),
                    const SizedBox(width: 4),
                  ],
                  Text(
                    effectiveValue.toString(),
                    style: AppTheme.bodyMedium.copyWith(
                      color: hasBonus ? AppTheme.successColor : Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 4),
          LinearProgressIndicator(
            value: effectiveValue / 100.0,
            backgroundColor: Colors.white12,
            valueColor: AlwaysStoppedAnimation<Color>(
              hasBonus ? AppTheme.successColor : AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAbilities(GameCard card) {
    if (card.abilities == null || card.abilities!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Abilities',
          style: AppTheme.headingSmall.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 12),
        ...card.abilities!.entries.map((entry) {
          return _buildAbilityItem(entry.key, entry.value);
        }),
      ],
    );
  }

  Widget _buildAbilityItem(String name, dynamic value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.darkCard,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.auto_awesome,
              color: AppTheme.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _formatAbilityName(name),
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (value is num && value != 0) ...[
                    const SizedBox(height: 2),
                    Text(
                      '${value > 0 ? '+' : ''}$value%',
                      style: AppTheme.bodySmall.copyWith(
                        color: value > 0 ? AppTheme.successColor : AppTheme.errorColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Level ${widget.playerCard.level}',
                style: AppTheme.headingSmall.copyWith(
                  color: Colors.white,
                ),
              ),
              Text(
                '${widget.playerCard.experience} XP',
                style: AppTheme.bodyMedium.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: widget.playerCard.experienceProgress,
            backgroundColor: Colors.white12,
            valueColor: const AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
          const SizedBox(height: 4),
          Text(
            '${widget.playerCard.experienceToNextLevel} XP to next level',
            style: AppTheme.bodySmall.copyWith(
              color: Colors.white54,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActions() {
    final user = ref.watch(currentUserProvider);
    final canAffordUpgrade = user != null && user.coins >= widget.playerCard.upgradeCost;

    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: PrimaryButton(
            text: 'Upgrade (${widget.playerCard.upgradeCost} coins)',
            onPressed: canAffordUpgrade ? _upgradeCard : null,
            icon: Icons.upgrade,
          ),
        ),
        const SizedBox(height: 12),
        SizedBox(
          width: double.infinity,
          child: SecondaryButton(
            text: widget.playerCard.isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
            onPressed: _toggleFavorite,
            icon: widget.playerCard.isFavorite ? Icons.favorite : Icons.favorite_border,
          ),
        ),
      ],
    );
  }

  void _upgradeCard() async {
    await ref.read(cardCollectionProvider.notifier).upgradeCard(widget.playerCard.playerCardId);
    // Refresh user data to update coins
    ref.read(authProvider.notifier).refreshUserData();
  }

  void _toggleFavorite() async {
    await ref.read(cardCollectionProvider.notifier).toggleFavorite(widget.playerCard.playerCardId);
  }

  IconData _getCardTypeIcon(String type) {
    switch (type.toLowerCase()) {
      case 'driver':
        return Icons.person;
      case 'codriver':
        return Icons.people;
      case 'vehicle':
        return Icons.directions_car;
      case 'strategy':
        return Icons.psychology;
      default:
        return Icons.help_outline;
    }
  }

  String _formatAbilityName(String name) {
    return name
        .split('_')
        .map((word) => word[0].toUpperCase() + word.substring(1))
        .join(' ');
  }
}
