const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { query } = require('../database/connection');

const router = express.Router();

// Get player profile
router.get('/profile', authenticateToken, async (req, res) => {
  try {
    const result = await query(
      'SELECT id, username, email, coins, gems, level, xp, created_at, last_login FROM players WHERE id = $1',
      [req.user.id]
    );

    if (result.rows.length === 0) {
      return res.status(404).json({ error: 'Player not found' });
    }

    res.json({ player: result.rows[0] });
  } catch (error) {
    console.error('Get player profile error:', error);
    res.status(500).json({ error: 'Failed to fetch player profile' });
  }
});

// Get player statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    // Get rally statistics
    const rallyStats = await query(`
      SELECT 
        COUNT(*) as total_rallies,
        AVG(final_position) as avg_position,
        MIN(final_position) as best_position,
        SUM(championship_points) as total_points
      FROM rally_results 
      WHERE player_id = $1
    `, [req.user.id]);

    // Get card collection stats
    const cardStats = await query(`
      SELECT 
        COUNT(*) as total_cards,
        COUNT(CASE WHEN cd.rarity = 'legendary' THEN 1 END) as legendary_cards,
        COUNT(CASE WHEN cd.rarity = 'epic' THEN 1 END) as epic_cards,
        COUNT(CASE WHEN cd.rarity = 'rare' THEN 1 END) as rare_cards,
        COUNT(CASE WHEN cd.rarity = 'common' THEN 1 END) as common_cards
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1
    `, [req.user.id]);

    res.json({
      rallyStats: rallyStats.rows[0],
      cardStats: cardStats.rows[0]
    });
  } catch (error) {
    console.error('Get player stats error:', error);
    res.status(500).json({ error: 'Failed to fetch player statistics' });
  }
});

module.exports = router;
