import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/card_provider.dart';
import '../../widgets/card_widget.dart';
import '../../widgets/custom_text_field.dart';
import '../../widgets/custom_button.dart';
import 'card_detail_screen.dart';

class CardCollectionScreen extends ConsumerStatefulWidget {
  const CardCollectionScreen({super.key});

  @override
  ConsumerState<CardCollectionScreen> createState() => _CardCollectionScreenState();
}

class _CardCollectionScreenState extends ConsumerState<CardCollectionScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _isGridView = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
    
    // Load collection on init
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(cardCollectionProvider.notifier).loadCollection();
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final collectionState = ref.watch(cardCollectionProvider);
    final filteredCards = ref.watch(filteredCardsProvider);
    final filter = ref.watch(cardFilterProvider);

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: SafeArea(
        child: Column(
          children: [
            // Header
            _buildHeader(),
            
            // Search and filters
            _buildSearchAndFilters(),
            
            // Tab bar
            _buildTabBar(),
            
            // Content
            Expanded(
              child: collectionState.isLoading
                ? _buildLoadingState()
                : collectionState.error != null
                  ? _buildErrorState(collectionState.error!)
                  : _buildCardGrid(filteredCards),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Card Collection',
                  style: AppTheme.headingMedium.copyWith(
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Consumer(
                  builder: (context, ref, child) {
                    final collectionState = ref.watch(cardCollectionProvider);
                    return Text(
                      '${collectionState.cards.length} cards collected',
                      style: AppTheme.bodySmall.copyWith(
                        color: Colors.white70,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: Colors.white70,
            ),
          ),
          IconButton(
            onPressed: _showFilterDialog,
            icon: const Icon(
              Icons.filter_list,
              color: Colors.white70,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchAndFilters() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: Column(
        children: [
          SearchTextField(
            controller: _searchController,
            hint: 'Search cards...',
            onChanged: (query) {
              ref.read(cardFilterProvider.notifier).setSearchQuery(query);
            },
            onClear: () {
              ref.read(cardFilterProvider.notifier).setSearchQuery('');
            },
          ),
          const SizedBox(height: 12),
          _buildQuickFilters(),
        ],
      ),
    );
  }

  Widget _buildQuickFilters() {
    final filter = ref.watch(cardFilterProvider);
    
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: [
          _buildFilterChip(
            label: 'Favorites',
            isSelected: filter.favoritesOnly,
            onTap: () {
              ref.read(cardFilterProvider.notifier).setFavoritesOnly(!filter.favoritesOnly);
            },
          ),
          const SizedBox(width: 8),
          ...AppConstants.cardRarities.map((rarity) {
            return Padding(
              padding: const EdgeInsets.only(right: 8),
              child: _buildFilterChip(
                label: rarity.toUpperCase(),
                isSelected: filter.rarity == rarity,
                color: AppTheme.getRarityColor(rarity),
                onTap: () {
                  final newRarity = filter.rarity == rarity ? null : rarity;
                  ref.read(cardFilterProvider.notifier).setRarity(newRarity);
                },
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget _buildFilterChip({
    required String label,
    required bool isSelected,
    Color? color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: isSelected 
            ? (color ?? AppTheme.primaryColor).withOpacity(0.2)
            : Colors.transparent,
          border: Border.all(
            color: isSelected 
              ? (color ?? AppTheme.primaryColor)
              : Colors.white38,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: isSelected 
              ? (color ?? AppTheme.primaryColor)
              : Colors.white70,
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.w400,
          ),
        ),
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: AppConstants.defaultPadding),
      child: TabBar(
        controller: _tabController,
        isScrollable: true,
        indicatorColor: AppTheme.primaryColor,
        labelColor: AppTheme.primaryColor,
        unselectedLabelColor: Colors.white54,
        labelStyle: AppTheme.bodySmall.copyWith(fontWeight: FontWeight.w600),
        unselectedLabelStyle: AppTheme.bodySmall,
        onTap: (index) {
          final types = ['all', ...AppConstants.cardTypes];
          final selectedType = index == 0 ? null : types[index];
          ref.read(cardFilterProvider.notifier).setType(selectedType);
        },
        tabs: const [
          Tab(text: 'ALL'),
          Tab(text: 'DRIVERS'),
          Tab(text: 'CO-DRIVERS'),
          Tab(text: 'VEHICLES'),
          Tab(text: 'STRATEGY'),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(
        color: AppTheme.primaryColor,
      ),
    );
  }

  Widget _buildErrorState(String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.error_outline,
              size: 64,
              color: AppTheme.errorColor,
            ),
            const SizedBox(height: 16),
            Text(
              'Failed to load cards',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.white,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              error,
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white70,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            PrimaryButton(
              text: 'Retry',
              onPressed: () {
                ref.read(cardCollectionProvider.notifier).loadCollection();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCardGrid(List<dynamic> cards) {
    if (cards.isEmpty) {
      return _buildEmptyState();
    }

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: _isGridView ? _buildGridView(cards) : _buildListView(cards),
    );
  }

  Widget _buildGridView(List<dynamic> cards) {
    return GridView.builder(
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: AppConstants.cardAspectRatio,
        crossAxisSpacing: 12,
        mainAxisSpacing: 12,
      ),
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        return CardWidget(
          playerCard: card,
          onTap: () => _showCardDetail(card),
          onFavorite: () => _toggleFavorite(card.playerCardId),
        ).animate().fadeIn(
          delay: (index * 50).ms,
          duration: 300.ms,
        ).slideY(
          begin: 0.3,
          end: 0,
        );
      },
    );
  }

  Widget _buildListView(List<dynamic> cards) {
    return ListView.builder(
      itemCount: cards.length,
      itemBuilder: (context, index) {
        final card = cards[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: CompactCardWidget(
            playerCard: card,
            onTap: () => _showCardDetail(card),
            onFavorite: () => _toggleFavorite(card.playerCardId),
          ).animate().fadeIn(
            delay: (index * 30).ms,
            duration: 200.ms,
          ).slideX(
            begin: -0.3,
            end: 0,
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConstants.defaultPadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(
              Icons.style_outlined,
              size: 64,
              color: Colors.white38,
            ),
            const SizedBox(height: 16),
            Text(
              'No cards found',
              style: AppTheme.headingSmall.copyWith(
                color: Colors.white70,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or visit the shop to get new cards',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white54,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            SecondaryButton(
              text: 'Clear Filters',
              onPressed: () {
                ref.read(cardFilterProvider.notifier).clearFilters();
                _searchController.clear();
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCardDetail(dynamic card) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => CardDetailScreen(playerCard: card),
      ),
    );
  }

  void _toggleFavorite(int playerCardId) {
    ref.read(cardCollectionProvider.notifier).toggleFavorite(playerCardId);
  }

  void _showFilterDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppTheme.darkSurface,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => const FilterBottomSheet(),
    );
  }
}

class FilterBottomSheet extends ConsumerWidget {
  const FilterBottomSheet({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final filter = ref.watch(cardFilterProvider);

    return Padding(
      padding: const EdgeInsets.all(AppConstants.defaultPadding),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Filter & Sort',
            style: AppTheme.headingSmall.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 16),
          
          // Sort options
          Text(
            'Sort by',
            style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            children: CardSortBy.values.map((sortBy) {
              return ChoiceChip(
                label: Text(sortBy.label),
                selected: filter.sortBy == sortBy,
                onSelected: (selected) {
                  if (selected) {
                    ref.read(cardFilterProvider.notifier).setSortBy(sortBy);
                  }
                },
              );
            }).toList(),
          ),
          
          const SizedBox(height: 24),
          
          // Clear filters button
          SizedBox(
            width: double.infinity,
            child: SecondaryButton(
              text: 'Clear All Filters',
              onPressed: () {
                ref.read(cardFilterProvider.notifier).clearFilters();
                Navigator.of(context).pop();
              },
            ),
          ),
        ],
      ),
    );
  }
}
