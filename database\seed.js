const fs = require('fs');
const path = require('path');
const { query, testConnection } = require('./connection');

// Execute a single seed file
const executeSeed = async (filename) => {
  const filePath = path.join(__dirname, 'seeds', filename);
  const sql = fs.readFileSync(filePath, 'utf8');
  
  console.log(`🌱 Executing seed: ${filename}`);
  
  try {
    // Execute the seed SQL
    await query(sql);
    console.log(`✅ Seed completed: ${filename}`);
  } catch (error) {
    console.error(`❌ Seed failed: ${filename}`);
    throw error;
  }
};

// Run all seed files
const runSeeds = async () => {
  console.log('🌱 Starting database seeding...');
  
  // Test database connection
  const isConnected = await testConnection();
  if (!isConnected) {
    console.error('❌ Database connection failed. Please check your configuration.');
    process.exit(1);
  }
  
  // Get list of seed files
  const seedsDir = path.join(__dirname, 'seeds');
  const seedFiles = fs.readdirSync(seedsDir)
    .filter(file => file.endsWith('.sql'))
    .sort();
  
  if (seedFiles.length === 0) {
    console.log('📝 No seed files found.');
    return;
  }
  
  console.log(`📋 Found ${seedFiles.length} seed files:`);
  seedFiles.forEach(file => console.log(`   - ${file}`));
  
  // Execute seed files in order
  for (const filename of seedFiles) {
    await executeSeed(filename);
  }
  
  console.log('🎉 All seeds completed successfully!');
  
  // Show summary
  await showSummary();
};

// Show database summary after seeding
const showSummary = async () => {
  console.log('\n📊 Database Summary:');
  
  try {
    // Count cards by type and rarity
    const cardStats = await query(`
      SELECT 
        type,
        rarity,
        COUNT(*) as count
      FROM card_definitions 
      WHERE is_active = true
      GROUP BY type, rarity
      ORDER BY type, 
        CASE rarity 
          WHEN 'legendary' THEN 1 
          WHEN 'epic' THEN 2 
          WHEN 'rare' THEN 3 
          WHEN 'common' THEN 4 
        END
    `);
    
    console.log('\n🃏 Cards by Type and Rarity:');
    let currentType = '';
    for (const row of cardStats.rows) {
      if (row.type !== currentType) {
        currentType = row.type;
        console.log(`\n  ${currentType.toUpperCase()}:`);
      }
      console.log(`    ${row.rarity}: ${row.count} cards`);
    }
    
    // Count shop items
    const shopStats = await query(`
      SELECT 
        type,
        COUNT(*) as count
      FROM shop_items 
      WHERE is_active = true
      GROUP BY type
      ORDER BY type
    `);
    
    console.log('\n🛒 Shop Items:');
    for (const row of shopStats.rows) {
      console.log(`  ${row.type}: ${row.count} items`);
    }
    
    // Count rally events
    const rallyStats = await query(`
      SELECT 
        championship_tier,
        COUNT(*) as count
      FROM rally_events 
      WHERE is_active = true
      GROUP BY championship_tier
      ORDER BY 
        CASE championship_tier 
          WHEN 'bronze' THEN 1 
          WHEN 'silver' THEN 2 
          WHEN 'gold' THEN 3 
          WHEN 'elite' THEN 4 
        END
    `);
    
    console.log('\n🏁 Rally Events:');
    for (const row of rallyStats.rows) {
      console.log(`  ${row.championship_tier}: ${row.count} events`);
    }
    
  } catch (error) {
    console.error('❌ Error generating summary:', error);
  }
};

// Clear all data and re-seed
const resetSeeds = async () => {
  console.log('⚠️  CLEARING ALL DATA - This will delete all game data!');
  
  try {
    // Clear tables in reverse dependency order
    await query('DELETE FROM player_achievements');
    await query('DELETE FROM achievements');
    await query('DELETE FROM player_decks');
    await query('DELETE FROM leaderboards');
    await query('DELETE FROM purchases');
    await query('DELETE FROM shop_items');
    await query('DELETE FROM rally_results');
    await query('DELETE FROM rally_events');
    await query('DELETE FROM player_cards');
    await query('DELETE FROM card_definitions');
    
    console.log('🗑️  All data cleared');
    
    console.log('🔄 Re-seeding database...');
    await runSeeds();
    
  } catch (error) {
    console.error('❌ Reset failed:', error);
    throw error;
  }
};

// Command line interface
const main = async () => {
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'run':
        await runSeeds();
        break;
      case 'reset':
        await resetSeeds();
        break;
      default:
        console.log('Usage:');
        console.log('  node seed.js run   - Run all seed files');
        console.log('  node seed.js reset - Clear all data and re-seed');
        break;
    }
  } catch (error) {
    console.error('❌ Seeding error:', error);
    process.exit(1);
  }
  
  process.exit(0);
};

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  runSeeds,
  resetSeeds
};
