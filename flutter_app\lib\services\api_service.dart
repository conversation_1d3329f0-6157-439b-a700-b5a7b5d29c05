import 'dart:convert';
import 'dart:io';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

import '../constants/app_constants.dart';
import '../models/user.dart';
import '../models/card.dart';
import 'storage_service.dart';

class ApiService {
  static final ApiService _instance = ApiService._internal();
  static ApiService get instance => _instance;
  ApiService._internal();

  late Dio _dio;
  String? _accessToken;

  Future<void> init() async {
    _dio = Dio(BaseOptions(
      baseUrl: AppConstants.baseUrl,
      connectTimeout: AppConstants.apiTimeout,
      receiveTimeout: AppConstants.apiTimeout,
      sendTimeout: AppConstants.apiTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    ));

    // Add interceptors
    _dio.interceptors.add(LogInterceptor(
      requestBody: kDebugMode,
      responseBody: kDebugMode,
      logPrint: (obj) => debugPrint(obj.toString()),
    ));

    _dio.interceptors.add(InterceptorsWrapper(
      onRequest: (options, handler) async {
        // Add auth token if available
        if (_accessToken != null) {
          options.headers['Authorization'] = 'Bearer $_accessToken';
        }
        handler.next(options);
      },
      onError: (error, handler) async {
        // Handle token refresh on 401
        if (error.response?.statusCode == 401) {
          final refreshed = await _refreshToken();
          if (refreshed) {
            // Retry the request
            final options = error.requestOptions;
            options.headers['Authorization'] = 'Bearer $_accessToken';
            try {
              final response = await _dio.fetch(options);
              handler.resolve(response);
              return;
            } catch (e) {
              // If retry fails, continue with original error
            }
          }
        }
        handler.next(error);
      },
    ));

    // Load stored token
    _accessToken = await StorageService.instance.getAccessToken();
  }

  // Set access token
  void setAccessToken(String? token) {
    _accessToken = token;
  }

  // Refresh token
  Future<bool> _refreshToken() async {
    try {
      final refreshToken = await StorageService.instance.getRefreshToken();
      if (refreshToken == null) return false;

      final response = await _dio.post('/auth/refresh', data: {
        'refreshToken': refreshToken,
      });

      if (response.statusCode == 200) {
        final data = response.data;
        _accessToken = data['accessToken'];
        await StorageService.instance.saveTokens(
          _accessToken!,
          data['refreshToken'],
        );
        return true;
      }
    } catch (e) {
      debugPrint('Token refresh failed: $e');
    }
    return false;
  }

  // Authentication endpoints
  Future<ApiResponse<AuthResponse>> login(String email, String password) async {
    try {
      final response = await _dio.post('/auth/login', data: {
        'email': email,
        'password': password,
      });

      if (response.statusCode == 200) {
        final authResponse = AuthResponse.fromJson(response.data);
        _accessToken = authResponse.accessToken;
        await StorageService.instance.saveTokens(
          authResponse.accessToken,
          authResponse.refreshToken,
        );
        await StorageService.instance.saveUserData(authResponse.user);
        return ApiResponse.success(authResponse);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Login failed');
  }

  Future<ApiResponse<AuthResponse>> register(
    String username,
    String email,
    String password,
  ) async {
    try {
      final response = await _dio.post('/auth/register', data: {
        'username': username,
        'email': email,
        'password': password,
      });

      if (response.statusCode == 201) {
        final authResponse = AuthResponse.fromJson(response.data);
        _accessToken = authResponse.accessToken;
        await StorageService.instance.saveTokens(
          authResponse.accessToken,
          authResponse.refreshToken,
        );
        await StorageService.instance.saveUserData(authResponse.user);
        return ApiResponse.success(authResponse);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Registration failed');
  }

  Future<ApiResponse<User>> getProfile() async {
    try {
      final response = await _dio.get('/auth/profile');
      if (response.statusCode == 200) {
        final user = User.fromJson(response.data['user']);
        await StorageService.instance.saveUserData(user);
        return ApiResponse.success(user);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to fetch profile');
  }

  // Player endpoints
  Future<ApiResponse<UserStats>> getPlayerStats() async {
    try {
      final response = await _dio.get('/player/stats');
      if (response.statusCode == 200) {
        return ApiResponse.success(UserStats.fromJson(response.data));
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to fetch player stats');
  }

  // Card endpoints
  Future<ApiResponse<List<PlayerCard>>> getCardCollection() async {
    try {
      final response = await _dio.get('/cards/collection');
      if (response.statusCode == 200) {
        final cards = (response.data['cards'] as List)
            .map((json) => PlayerCard.fromJson(json))
            .toList();
        return ApiResponse.success(cards);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to fetch card collection');
  }

  Future<ApiResponse<List<GameCard>>> getCardCatalog({
    String? type,
    String? rarity,
  }) async {
    try {
      final queryParams = <String, dynamic>{};
      if (type != null) queryParams['type'] = type;
      if (rarity != null) queryParams['rarity'] = rarity;

      final response = await _dio.get('/cards/catalog', queryParameters: queryParams);
      if (response.statusCode == 200) {
        final cards = (response.data['cards'] as List)
            .map((json) => GameCard.fromJson(json))
            .toList();
        return ApiResponse.success(cards);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to fetch card catalog');
  }

  Future<ApiResponse<PlayerCard>> upgradeCard(int playerCardId) async {
    try {
      final response = await _dio.post('/cards/upgrade/$playerCardId');
      if (response.statusCode == 200) {
        return ApiResponse.success(PlayerCard.fromJson(response.data['playerCard']));
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to upgrade card');
  }

  Future<ApiResponse<bool>> toggleCardFavorite(int playerCardId) async {
    try {
      final response = await _dio.post('/cards/favorite/$playerCardId');
      if (response.statusCode == 200) {
        return ApiResponse.success(response.data['isFavorite']);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to toggle favorite');
  }

  // Shop endpoints
  Future<ApiResponse<List<ShopItem>>> getShopItems() async {
    try {
      final response = await _dio.get('/shop/items');
      if (response.statusCode == 200) {
        final items = (response.data['items'] as List)
            .map((json) => ShopItem.fromJson(json))
            .toList();
        return ApiResponse.success(items);
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Failed to fetch shop items');
  }

  Future<ApiResponse<PurchaseResult>> purchaseItem(
    int itemId,
    String paymentMethod,
  ) async {
    try {
      final response = await _dio.post('/shop/purchase', data: {
        'itemId': itemId,
        'paymentMethod': paymentMethod,
      });
      if (response.statusCode == 200) {
        return ApiResponse.success(PurchaseResult.fromJson(response.data));
      }
    } catch (e) {
      return ApiResponse.error(_handleError(e));
    }
    return ApiResponse.error('Purchase failed');
  }

  // Logout
  Future<void> logout() async {
    _accessToken = null;
    await StorageService.instance.clearAll();
  }

  // Error handling
  String _handleError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Connection timeout. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          final message = error.response?.data?['error'] ?? 'Server error';
          return '$message (Status: $statusCode)';
        case DioExceptionType.cancel:
          return 'Request was cancelled';
        case DioExceptionType.unknown:
          if (error.error is SocketException) {
            return 'No internet connection';
          }
          return 'Network error occurred';
        default:
          return 'An unexpected error occurred';
      }
    }
    return error.toString();
  }
}

// Response wrapper
class ApiResponse<T> {
  final T? data;
  final String? error;
  final bool isSuccess;

  ApiResponse.success(this.data)
      : error = null,
        isSuccess = true;

  ApiResponse.error(this.error)
      : data = null,
        isSuccess = false;
}

// Auth response model
class AuthResponse {
  final User user;
  final String accessToken;
  final String refreshToken;

  AuthResponse({
    required this.user,
    required this.accessToken,
    required this.refreshToken,
  });

  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      user: User.fromJson(json['user']),
      accessToken: json['token'] ?? json['accessToken'],
      refreshToken: json['refreshToken'],
    );
  }
}

// Shop item model
class ShopItem {
  final int id;
  final String name;
  final String type;
  final int priceCoins;
  final int priceGems;
  final double priceRealMoney;
  final Map<String, dynamic> contents;

  ShopItem({
    required this.id,
    required this.name,
    required this.type,
    required this.priceCoins,
    required this.priceGems,
    required this.priceRealMoney,
    required this.contents,
  });

  factory ShopItem.fromJson(Map<String, dynamic> json) {
    return ShopItem(
      id: json['id'],
      name: json['name'],
      type: json['type'],
      priceCoins: json['price_coins'] ?? 0,
      priceGems: json['price_gems'] ?? 0,
      priceRealMoney: (json['price_real_money'] ?? 0.0).toDouble(),
      contents: json['contents'] ?? {},
    );
  }
}

// Purchase result model
class PurchaseResult {
  final bool success;
  final Map<String, dynamic> itemsReceived;
  final int playerCoins;
  final int playerGems;

  PurchaseResult({
    required this.success,
    required this.itemsReceived,
    required this.playerCoins,
    required this.playerGems,
  });

  factory PurchaseResult.fromJson(Map<String, dynamic> json) {
    return PurchaseResult(
      success: json['success'] ?? false,
      itemsReceived: json['itemsReceived'] ?? {},
      playerCoins: json['playerCoins'] ?? 0,
      playerGems: json['playerGems'] ?? 0,
    );
  }
}
