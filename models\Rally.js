const { query, beginTransaction, commitTransaction, rollbackTransaction } = require('../database/connection');
const { calculateStageTime, calculateCrashProbability, randomBetween } = require('../utils/helpers');
const { RALLY_REWARDS } = require('../utils/constants');

class Rally {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.championshipTier = data.championship_tier;
    this.stages = data.stages;
    this.entryCostCoins = data.entry_cost_coins;
    this.entryCostGems = data.entry_cost_gems;
    this.rewards = data.rewards;
    this.isActive = data.is_active;
    this.startDate = data.start_date;
    this.endDate = data.end_date;
    this.createdAt = data.created_at;
  }

  // Find rally by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM rally_events WHERE id = $1 AND is_active = true',
      [id]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Rally(result.rows[0]);
  }

  // Get all active rallies
  static async findAll() {
    const result = await query(`
      SELECT * FROM rally_events 
      WHERE is_active = true
      AND (start_date IS NULL OR start_date <= NOW())
      AND (end_date IS NULL OR end_date >= NOW())
      ORDER BY championship_tier, name
    `);
    
    return result.rows.map(row => new Rally(row));
  }

  // Get rallies by championship tier
  static async findByTier(tier) {
    const result = await query(
      'SELECT * FROM rally_events WHERE championship_tier = $1 AND is_active = true ORDER BY name',
      [tier]
    );
    
    return result.rows.map(row => new Rally(row));
  }

  // Create new rally event
  static async create(rallyData) {
    const { name, championshipTier, stages, entryCostCoins, entryCostGems, rewards, startDate, endDate } = rallyData;
    
    const result = await query(
      `INSERT INTO rally_events (name, championship_tier, stages, entry_cost_coins, entry_cost_gems, rewards, start_date, end_date) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [name, championshipTier, JSON.stringify(stages), entryCostCoins, entryCostGems, JSON.stringify(rewards), startDate, endDate]
    );
    
    return new Rally(result.rows[0]);
  }

  // Check if player can afford entry
  canPlayerAfford(playerCoins, playerGems) {
    return playerCoins >= this.entryCostCoins && playerGems >= this.entryCostGems;
  }

  // Simulate rally for a player with their deck
  async simulateRally(playerId, deck) {
    const client = await beginTransaction();
    
    try {
      // Validate deck has required cards
      if (!deck.driverId || !deck.codriverId || !deck.vehicleId) {
        throw new Error('Deck must have driver, co-driver, and vehicle');
      }

      // Get card stats
      const driverResult = await client.query(`
        SELECT cd.stats, cd.abilities, pc.level 
        FROM player_cards pc 
        JOIN card_definitions cd ON pc.card_id = cd.id 
        WHERE pc.id = $1 AND pc.player_id = $2
      `, [deck.driverId, playerId]);

      const codriverResult = await client.query(`
        SELECT cd.stats, cd.abilities, pc.level 
        FROM player_cards pc 
        JOIN card_definitions cd ON pc.card_id = cd.id 
        WHERE pc.id = $1 AND pc.player_id = $2
      `, [deck.codriverId, playerId]);

      const vehicleResult = await client.query(`
        SELECT cd.stats, cd.abilities, pc.level 
        FROM player_cards pc 
        JOIN card_definitions cd ON pc.card_id = cd.id 
        WHERE pc.id = $1 AND pc.player_id = $2
      `, [deck.vehicleId, playerId]);

      if (driverResult.rows.length === 0 || codriverResult.rows.length === 0 || vehicleResult.rows.length === 0) {
        throw new Error('Invalid deck: cards not found or not owned by player');
      }

      const driver = driverResult.rows[0];
      const codriver = codriverResult.rows[0];
      const vehicle = vehicleResult.rows[0];

      // Simulate each stage
      const stageResults = [];
      let totalTime = 0;
      let crashed = false;
      let damage = 0;

      for (let i = 0; i < this.stages.length; i++) {
        const stage = this.stages[i];
        
        // Calculate base time (in milliseconds)
        const baseTime = stage.length * 60000; // 1 minute per km base time
        
        // Apply difficulty multiplier
        const difficultyMultiplier = 1 + (stage.difficulty - 1) * 0.1;
        const adjustedBaseTime = baseTime * difficultyMultiplier;

        // Calculate stage time with all factors
        const stageTime = calculateStageTime(
          adjustedBaseTime,
          driver.stats,
          vehicle.stats,
          { weather: stage.weather, surface: stage.surface },
          'balanced' // Default risk level for simulation
        );

        // Check for crash
        const crashChance = calculateCrashProbability(
          driver.stats,
          vehicle.stats,
          { weather: stage.weather, surface: stage.surface },
          'balanced'
        );

        const crashRoll = Math.random();
        const stageCrashed = crashRoll < crashChance;

        let finalStageTime = stageTime;
        if (stageCrashed) {
          // Add penalty time for crash (30-60 seconds)
          const penaltyTime = randomBetween(30000, 60000);
          finalStageTime += penaltyTime;
          damage += randomBetween(10, 25);
          crashed = true;
        }

        // Apply damage penalty to subsequent stages
        if (damage > 0) {
          const damagePenalty = 1 + (damage / 100);
          finalStageTime *= damagePenalty;
        }

        stageResults.push({
          stageIndex: i,
          stageName: stage.name,
          time: finalStageTime,
          crashed: stageCrashed,
          damage: damage,
          position: 1 // Will be calculated after all players finish
        });

        totalTime += finalStageTime;
      }

      // Calculate final position (simplified - random for now)
      const finalPosition = randomBetween(1, 20);
      
      // Calculate rewards based on position
      const championshipPoints = RALLY_REWARDS.CHAMPIONSHIP_POINTS[finalPosition] || 0;
      const baseCoins = RALLY_REWARDS.BASE_COINS[finalPosition] || 0;
      
      const rewardsEarned = {
        championshipPoints,
        coins: baseCoins,
        xp: this.rewards.xp || 50
      };

      // Save rally result
      const resultData = await client.query(
        `INSERT INTO rally_results (player_id, rally_id, deck_used, stage_results, total_time, final_position, championship_points, rewards_earned)
         VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
         RETURNING *`,
        [
          playerId,
          this.id,
          JSON.stringify(deck),
          JSON.stringify(stageResults),
          totalTime,
          finalPosition,
          championshipPoints,
          JSON.stringify(rewardsEarned)
        ]
      );

      await commitTransaction(client);

      return {
        rallyResult: resultData.rows[0],
        stageResults,
        totalTime,
        finalPosition,
        rewards: rewardsEarned,
        crashed
      };

    } catch (error) {
      await rollbackTransaction(client);
      throw error;
    }
  }

  // Get leaderboard for this rally
  async getLeaderboard(limit = 50) {
    const result = await query(`
      SELECT 
        rr.final_position,
        rr.total_time,
        rr.championship_points,
        rr.completed_at,
        p.username,
        p.level
      FROM rally_results rr
      JOIN players p ON rr.player_id = p.id
      WHERE rr.rally_id = $1
      ORDER BY rr.final_position ASC, rr.total_time ASC
      LIMIT $2
    `, [this.id, limit]);

    return result.rows;
  }

  // Get player's best result for this rally
  async getPlayerBestResult(playerId) {
    const result = await query(`
      SELECT * FROM rally_results 
      WHERE rally_id = $1 AND player_id = $2
      ORDER BY final_position ASC, total_time ASC
      LIMIT 1
    `, [this.id, playerId]);

    return result.rows.length > 0 ? result.rows[0] : null;
  }

  // Update rally
  async update(updates) {
    const allowedFields = ['name', 'stages', 'entry_cost_coins', 'entry_cost_gems', 'rewards', 'is_active', 'start_date', 'end_date'];
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field)) {
        updateFields.push(`${field} = $${paramCount}`);
        
        // JSON stringify objects
        if (field === 'stages' || field === 'rewards') {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
        paramCount++;
      }
    }

    if (updateFields.length === 0) {
      return this;
    }

    values.push(this.id);
    const result = await query(
      `UPDATE rally_events SET ${updateFields.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length > 0) {
      Object.assign(this, result.rows[0]);
    }

    return this;
  }

  // Deactivate rally
  async deactivate() {
    await this.update({ isActive: false });
    return this;
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      championshipTier: this.championshipTier,
      stages: this.stages,
      entryCostCoins: this.entryCostCoins,
      entryCostGems: this.entryCostGems,
      rewards: this.rewards,
      isActive: this.isActive,
      startDate: this.startDate,
      endDate: this.endDate,
      createdAt: this.createdAt
    };
  }
}

module.exports = Rally;
