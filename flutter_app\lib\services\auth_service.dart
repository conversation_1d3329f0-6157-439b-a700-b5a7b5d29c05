import '../models/user.dart';
import '../services/api_service.dart';
import '../services/storage_service.dart';

class AuthService {
  final ApiService _apiService = ApiService.instance;
  final StorageService _storageService = StorageService.instance;

  // Login user
  Future<AuthResult> login(String email, String password) async {
    try {
      final response = await _apiService.login(email, password);
      
      if (response.isSuccess && response.data != null) {
        return AuthResult.success(response.data!.user);
      } else {
        return AuthResult.error(response.error ?? 'Login failed');
      }
    } catch (e) {
      return AuthResult.error('Login failed: ${e.toString()}');
    }
  }

  // Register user
  Future<AuthResult> register(String username, String email, String password) async {
    try {
      final response = await _apiService.register(username, email, password);
      
      if (response.isSuccess && response.data != null) {
        return AuthResult.success(response.data!.user);
      } else {
        return AuthResult.error(response.error ?? 'Registration failed');
      }
    } catch (e) {
      return AuthResult.error('Registration failed: ${e.toString()}');
    }
  }

  // Get current user
  Future<User?> getCurrentUser() async {
    return await _storageService.getUserData();
  }

  // Refresh user data
  Future<AuthResult> refreshUserData() async {
    try {
      final response = await _apiService.getProfile();
      
      if (response.isSuccess && response.data != null) {
        return AuthResult.success(response.data!);
      } else {
        return AuthResult.error(response.error ?? 'Failed to refresh user data');
      }
    } catch (e) {
      return AuthResult.error('Failed to refresh user data: ${e.toString()}');
    }
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    return await _storageService.isLoggedIn();
  }

  // Logout user
  Future<void> logout() async {
    await _apiService.logout();
  }

  // Validate email format
  bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Validate username
  String? validateUsername(String username) {
    if (username.isEmpty) {
      return 'Username is required';
    }
    if (username.length < 3) {
      return 'Username must be at least 3 characters';
    }
    if (username.length > 20) {
      return 'Username must be at most 20 characters';
    }
    if (!RegExp(r'^[a-zA-Z0-9_]+$').hasMatch(username)) {
      return 'Username can only contain letters, numbers, and underscores';
    }
    return null;
  }

  // Validate email
  String? validateEmail(String email) {
    if (email.isEmpty) {
      return 'Email is required';
    }
    if (!isValidEmail(email)) {
      return 'Please enter a valid email address';
    }
    return null;
  }

  // Validate password
  String? validatePassword(String password) {
    if (password.isEmpty) {
      return 'Password is required';
    }
    if (password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    if (password.length > 100) {
      return 'Password must be at most 100 characters';
    }
    return null;
  }

  // Check password strength
  PasswordStrength getPasswordStrength(String password) {
    if (password.isEmpty) return PasswordStrength.none;
    
    int score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (RegExp(r'[a-z]').hasMatch(password)) score++;
    if (RegExp(r'[A-Z]').hasMatch(password)) score++;
    if (RegExp(r'[0-9]').hasMatch(password)) score++;
    if (RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password)) score++;
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    return PasswordStrength.strong;
  }
}

// Auth result wrapper
class AuthResult {
  final User? user;
  final String? error;
  final bool isSuccess;

  AuthResult.success(this.user)
      : error = null,
        isSuccess = true;

  AuthResult.error(this.error)
      : user = null,
        isSuccess = false;
}

// Password strength enum
enum PasswordStrength {
  none,
  weak,
  medium,
  strong,
}

extension PasswordStrengthExtension on PasswordStrength {
  String get label {
    switch (this) {
      case PasswordStrength.none:
        return '';
      case PasswordStrength.weak:
        return 'Weak';
      case PasswordStrength.medium:
        return 'Medium';
      case PasswordStrength.strong:
        return 'Strong';
    }
  }

  double get progress {
    switch (this) {
      case PasswordStrength.none:
        return 0.0;
      case PasswordStrength.weak:
        return 0.33;
      case PasswordStrength.medium:
        return 0.66;
      case PasswordStrength.strong:
        return 1.0;
    }
  }
}
