-- Rally Championship Manager Database Schema
-- Initial migration: Create all core tables

-- Players and Authentication
CREATE TABLE players (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    coins INTEGER DEFAULT 1000,
    gems INTEGER DEFAULT 100,
    level INTEGER DEFAULT 1,
    xp INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT NOW(),
    last_login TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- Card Definitions (Master Data)
CREATE TABLE card_definitions (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(20) NOT NULL, -- 'driver', 'codriver', 'vehicle', 'strategy'
    rarity VARCHAR(20) NOT NULL, -- 'common', 'rare', 'epic', 'legendary'
    stats JSONB NOT NULL, -- {'speed': 85, 'handling': 92, 'consistency': 78}
    abilities JSONB, -- Special abilities and effects
    image_url VARCHAR(255),
    description TEXT,
    flavor_text TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Player Card Collection
CREATE TABLE player_cards (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id) ON DELETE CASCADE,
    card_id INTEGER REFERENCES card_definitions(id),
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    obtained_at TIMESTAMP DEFAULT NOW(),
    is_favorite BOOLEAN DEFAULT false
);

-- Rally Events (Championships)
CREATE TABLE rally_events (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    championship_tier VARCHAR(20) NOT NULL, -- 'bronze', 'silver', 'gold', 'elite'
    stages JSONB NOT NULL, -- Array of stage configurations
    entry_cost_coins INTEGER DEFAULT 0,
    entry_cost_gems INTEGER DEFAULT 0,
    rewards JSONB NOT NULL, -- Rewards structure
    is_active BOOLEAN DEFAULT true,
    start_date TIMESTAMP,
    end_date TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Player Rally Results
CREATE TABLE rally_results (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    rally_id INTEGER REFERENCES rally_events(id),
    deck_used JSONB NOT NULL, -- Cards used in this rally
    stage_results JSONB NOT NULL, -- Individual stage times and results
    total_time INTEGER NOT NULL, -- Total rally time in milliseconds
    final_position INTEGER NOT NULL,
    championship_points INTEGER DEFAULT 0,
    rewards_earned JSONB,
    completed_at TIMESTAMP DEFAULT NOW()
);

-- Card Packs and Shop Items
CREATE TABLE shop_items (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'card_pack', 'currency', 'cosmetic'
    price_coins INTEGER DEFAULT 0,
    price_gems INTEGER DEFAULT 0,
    price_real_money DECIMAL(10,2) DEFAULT 0,
    contents JSONB NOT NULL, -- What's in the pack
    is_active BOOLEAN DEFAULT true,
    limited_quantity INTEGER, -- NULL for unlimited
    available_until TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Purchase History
CREATE TABLE purchases (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    item_id INTEGER REFERENCES shop_items(id),
    payment_method VARCHAR(50), -- 'coins', 'gems', 'real_money'
    amount_paid DECIMAL(10,2),
    items_received JSONB,
    transaction_id VARCHAR(255), -- For real money purchases
    purchased_at TIMESTAMP DEFAULT NOW()
);

-- Global Leaderboards
CREATE TABLE leaderboards (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    championship_tier VARCHAR(20),
    total_points INTEGER DEFAULT 0,
    rallies_completed INTEGER DEFAULT 0,
    average_position DECIMAL(5,2),
    best_rally_time INTEGER,
    season VARCHAR(20), -- '2024_spring', etc.
    last_updated TIMESTAMP DEFAULT NOW()
);

-- Player Decks (Rally Team Configurations)
CREATE TABLE player_decks (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    name VARCHAR(50) NOT NULL,
    driver_card_id INTEGER REFERENCES player_cards(id),
    codriver_card_id INTEGER REFERENCES player_cards(id),
    vehicle_card_id INTEGER REFERENCES player_cards(id),
    strategy_cards JSONB NOT NULL, -- Array of strategy card IDs
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Daily Challenges and Achievements
CREATE TABLE achievements (
    id SERIAL PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    description TEXT NOT NULL,
    type VARCHAR(50) NOT NULL, -- 'rally', 'collection', 'progression'
    requirements JSONB NOT NULL, -- Achievement requirements
    rewards JSONB NOT NULL, -- Coins, gems, cards, etc.
    is_active BOOLEAN DEFAULT true
);

-- Player Achievement Progress
CREATE TABLE player_achievements (
    id SERIAL PRIMARY KEY,
    player_id INTEGER REFERENCES players(id),
    achievement_id INTEGER REFERENCES achievements(id),
    progress JSONB DEFAULT '{}', -- Current progress towards achievement
    completed_at TIMESTAMP,
    claimed_at TIMESTAMP,
    UNIQUE(player_id, achievement_id)
);

-- Indexes for performance
CREATE INDEX idx_players_email ON players(email);
CREATE INDEX idx_players_username ON players(username);
CREATE INDEX idx_players_active ON players(is_active);

CREATE INDEX idx_card_definitions_type ON card_definitions(type);
CREATE INDEX idx_card_definitions_rarity ON card_definitions(rarity);
CREATE INDEX idx_card_definitions_active ON card_definitions(is_active);

CREATE INDEX idx_player_cards_player_id ON player_cards(player_id);
CREATE INDEX idx_player_cards_card_id ON player_cards(card_id);
CREATE INDEX idx_player_cards_favorite ON player_cards(player_id, is_favorite);

CREATE INDEX idx_rally_events_tier ON rally_events(championship_tier);
CREATE INDEX idx_rally_events_active ON rally_events(is_active);
CREATE INDEX idx_rally_events_dates ON rally_events(start_date, end_date);

CREATE INDEX idx_rally_results_player_id ON rally_results(player_id);
CREATE INDEX idx_rally_results_rally_id ON rally_results(rally_id);
CREATE INDEX idx_rally_results_position ON rally_results(final_position);
CREATE INDEX idx_rally_results_completed ON rally_results(completed_at);

CREATE INDEX idx_shop_items_active ON shop_items(is_active);
CREATE INDEX idx_shop_items_type ON shop_items(type);

CREATE INDEX idx_purchases_player_id ON purchases(player_id);
CREATE INDEX idx_purchases_date ON purchases(purchased_at);

CREATE INDEX idx_leaderboards_tier_points ON leaderboards(championship_tier, total_points DESC);
CREATE INDEX idx_leaderboards_season ON leaderboards(season);

CREATE INDEX idx_player_decks_player_id ON player_decks(player_id);
CREATE INDEX idx_player_decks_active ON player_decks(player_id, is_active);

CREATE INDEX idx_player_achievements_player_id ON player_achievements(player_id);
CREATE INDEX idx_player_achievements_completed ON player_achievements(completed_at);
