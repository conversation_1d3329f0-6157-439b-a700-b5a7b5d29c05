import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../constants/app_theme.dart';
import '../constants/app_constants.dart';
import '../models/card.dart';

class CardWidget extends StatefulWidget {
  final PlayerCard? playerCard;
  final GameCard? gameCard;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool showLevel;
  final bool showFavorite;
  final bool isSelected;
  final double? width;
  final double? height;

  const CardWidget({
    super.key,
    this.playerCard,
    this.gameCard,
    this.onTap,
    this.onFavorite,
    this.showLevel = true,
    this.showFavorite = true,
    this.isSelected = false,
    this.width,
    this.height,
  }) : assert(playerCard != null || gameCard != null, 'Either playerCard or gameCard must be provided');

  @override
  State<CardWidget> createState() => _CardWidgetState();
}

class _CardWidgetState extends State<CardWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  GameCard? get card => widget.playerCard?.card ?? widget.gameCard;
  bool get isFavorite => widget.playerCard?.isFavorite ?? false;
  int get level => widget.playerCard?.level ?? 1;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));

    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    _animationController.forward();
  }

  void _handleTapUp(TapUpDetails details) {
    _animationController.reverse();
  }

  void _handleTapCancel() {
    _animationController.reverse();
  }

  @override
  Widget build(BuildContext context) {
    if (card == null) return const SizedBox.shrink();

    final cardWidth = widget.width ?? 140.0;
    final cardHeight = widget.height ?? (cardWidth / AppConstants.cardAspectRatio);

    return GestureDetector(
      onTapDown: _handleTapDown,
      onTapUp: _handleTapUp,
      onTapCancel: _handleTapCancel,
      onTap: widget.onTap,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: cardWidth,
              height: cardHeight,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.3),
                    blurRadius: 8 + (_glowAnimation.value * 4),
                    spreadRadius: 1 + (_glowAnimation.value * 2),
                  ),
                  if (widget.isSelected)
                    BoxShadow(
                      color: AppTheme.primaryColor.withOpacity(0.5),
                      blurRadius: 12,
                      spreadRadius: 3,
                    ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConstants.borderRadius),
                child: Stack(
                  children: [
                    // Background gradient
                    _buildBackground(),
                    
                    // Card content
                    _buildContent(),
                    
                    // Rarity border
                    _buildRarityBorder(),
                    
                    // Level indicator
                    if (widget.showLevel && widget.playerCard != null)
                      _buildLevelIndicator(),
                    
                    // Favorite indicator
                    if (widget.showFavorite && widget.playerCard != null)
                      _buildFavoriteIndicator(),
                    
                    // Selection overlay
                    if (widget.isSelected)
                      _buildSelectionOverlay(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            AppTheme.darkCard,
            AppTheme.darkCard.withOpacity(0.8),
            AppTheme.getRarityColor(card!.rarity).withOpacity(0.1),
          ],
        ),
      ),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(8.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Card image placeholder
          Expanded(
            flex: 3,
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.2),
                borderRadius: BorderRadius.circular(6),
                border: Border.all(
                  color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.3),
                ),
              ),
              child: Center(
                child: Icon(
                  _getCardTypeIcon(),
                  size: 32,
                  color: AppTheme.getRarityColor(card!.rarity),
                ),
              ),
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Card name
          Expanded(
            flex: 1,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  card!.name,
                  style: AppTheme.bodySmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 2),
                Text(
                  card!.type.toUpperCase(),
                  style: AppTheme.bodySmall.copyWith(
                    color: AppTheme.getRarityColor(card!.rarity),
                    fontSize: 10,
                    fontWeight: FontWeight.w500,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRarityBorder() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: AppTheme.getRarityColor(card!.rarity),
            width: 2,
          ),
        ),
      ),
    );
  }

  Widget _buildLevelIndicator() {
    return Positioned(
      top: 4,
      left: 4,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Text(
          'LV $level',
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white,
            fontSize: 10,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
    );
  }

  Widget _buildFavoriteIndicator() {
    return Positioned(
      top: 4,
      right: 4,
      child: GestureDetector(
        onTap: widget.onFavorite,
        child: Container(
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
            color: Colors.black.withOpacity(0.5),
            shape: BoxShape.circle,
          ),
          child: Icon(
            isFavorite ? Icons.favorite : Icons.favorite_border,
            size: 16,
            color: isFavorite ? AppTheme.errorColor : Colors.white70,
          ),
        ),
      ),
    );
  }

  Widget _buildSelectionOverlay() {
    return Positioned.fill(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          color: AppTheme.primaryColor.withOpacity(0.2),
          border: Border.all(
            color: AppTheme.primaryColor,
            width: 3,
          ),
        ),
        child: const Center(
          child: Icon(
            Icons.check_circle,
            color: AppTheme.primaryColor,
            size: 32,
          ),
        ),
      ),
    );
  }

  IconData _getCardTypeIcon() {
    switch (card!.type.toLowerCase()) {
      case 'driver':
        return Icons.person;
      case 'codriver':
        return Icons.people;
      case 'vehicle':
        return Icons.directions_car;
      case 'strategy':
        return Icons.psychology;
      default:
        return Icons.help_outline;
    }
  }
}

// Compact card widget for lists
class CompactCardWidget extends StatelessWidget {
  final PlayerCard? playerCard;
  final GameCard? gameCard;
  final VoidCallback? onTap;
  final VoidCallback? onFavorite;
  final bool showLevel;
  final bool showFavorite;

  const CompactCardWidget({
    super.key,
    this.playerCard,
    this.gameCard,
    this.onTap,
    this.onFavorite,
    this.showLevel = true,
    this.showFavorite = true,
  }) : assert(playerCard != null || gameCard != null);

  GameCard? get card => playerCard?.card ?? gameCard;
  bool get isFavorite => playerCard?.isFavorite ?? false;
  int get level => playerCard?.level ?? 1;

  @override
  Widget build(BuildContext context) {
    if (card == null) return const SizedBox.shrink();

    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.darkCard,
          borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          border: Border.all(
            color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            // Card icon
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.getRarityColor(card!.rarity).withOpacity(0.3),
                ),
              ),
              child: Icon(
                _getCardTypeIcon(),
                color: AppTheme.getRarityColor(card!.rarity),
                size: 20,
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Card info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    card!.name,
                    style: AppTheme.bodyMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      Text(
                        card!.type.toUpperCase(),
                        style: AppTheme.bodySmall.copyWith(
                          color: AppTheme.getRarityColor(card!.rarity),
                          fontSize: 11,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      if (showLevel && playerCard != null) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            'LV $level',
                            style: AppTheme.bodySmall.copyWith(
                              color: AppTheme.primaryColor,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ),
            ),
            
            // Favorite button
            if (showFavorite && playerCard != null)
              GestureDetector(
                onTap: onFavorite,
                child: Icon(
                  isFavorite ? Icons.favorite : Icons.favorite_border,
                  color: isFavorite ? AppTheme.errorColor : Colors.white54,
                  size: 20,
                ),
              ),
          ],
        ),
      ),
    );
  }

  IconData _getCardTypeIcon() {
    switch (card!.type.toLowerCase()) {
      case 'driver':
        return Icons.person;
      case 'codriver':
        return Icons.people;
      case 'vehicle':
        return Icons.directions_car;
      case 'strategy':
        return Icons.psychology;
      default:
        return Icons.help_outline;
    }
  }
}
