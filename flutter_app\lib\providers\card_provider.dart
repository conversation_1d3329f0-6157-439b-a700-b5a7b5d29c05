import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/card.dart';
import '../services/api_service.dart';

// Card collection provider
final cardCollectionProvider = StateNotifierProvider<CardCollectionNotifier, CardCollectionState>((ref) {
  return CardCollectionNotifier();
});

// Card catalog provider
final cardCatalogProvider = StateNotifierProvider<CardCatalogNotifier, CardCatalogState>((ref) {
  return CardCatalogNotifier();
});

// Filtered cards provider
final filteredCardsProvider = Provider<List<PlayerCard>>((ref) {
  final collection = ref.watch(cardCollectionProvider);
  final filter = ref.watch(cardFilterProvider);
  
  if (collection.cards.isEmpty) return [];
  
  var filteredCards = collection.cards.where((card) {
    if (filter.type != null && card.card?.type != filter.type) return false;
    if (filter.rarity != null && card.card?.rarity != filter.rarity) return false;
    if (filter.favoritesOnly && !card.isFavorite) return false;
    if (filter.searchQuery.isNotEmpty) {
      final query = filter.searchQuery.toLowerCase();
      final name = card.card?.name.toLowerCase() ?? '';
      if (!name.contains(query)) return false;
    }
    return true;
  }).toList();
  
  // Sort cards
  switch (filter.sortBy) {
    case CardSortBy.name:
      filteredCards.sort((a, b) => (a.card?.name ?? '').compareTo(b.card?.name ?? ''));
      break;
    case CardSortBy.rarity:
      filteredCards.sort((a, b) => _getRarityOrder(b.card?.rarity ?? '').compareTo(_getRarityOrder(a.card?.rarity ?? '')));
      break;
    case CardSortBy.level:
      filteredCards.sort((a, b) => b.level.compareTo(a.level));
      break;
    case CardSortBy.dateObtained:
      filteredCards.sort((a, b) => b.obtainedAt.compareTo(a.obtainedAt));
      break;
  }
  
  return filteredCards;
});

// Card filter provider
final cardFilterProvider = StateNotifierProvider<CardFilterNotifier, CardFilter>((ref) {
  return CardFilterNotifier();
});

// Helper function for rarity ordering
int _getRarityOrder(String rarity) {
  switch (rarity.toLowerCase()) {
    case 'legendary': return 4;
    case 'epic': return 3;
    case 'rare': return 2;
    case 'common': return 1;
    default: return 0;
  }
}

// Card Collection State
class CardCollectionState {
  final List<PlayerCard> cards;
  final bool isLoading;
  final String? error;

  const CardCollectionState({
    this.cards = const [],
    this.isLoading = false,
    this.error,
  });

  CardCollectionState copyWith({
    List<PlayerCard>? cards,
    bool? isLoading,
    String? error,
  }) {
    return CardCollectionState(
      cards: cards ?? this.cards,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Card Collection Notifier
class CardCollectionNotifier extends StateNotifier<CardCollectionState> {
  CardCollectionNotifier() : super(const CardCollectionState());

  Future<void> loadCollection() async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await ApiService.instance.getCardCollection();
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          cards: response.data!,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load card collection',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load card collection: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  Future<void> upgradeCard(int playerCardId) async {
    try {
      final response = await ApiService.instance.upgradeCard(playerCardId);
      
      if (response.isSuccess && response.data != null) {
        final updatedCard = response.data!;
        final updatedCards = state.cards.map((card) {
          return card.playerCardId == playerCardId ? updatedCard : card;
        }).toList();
        
        state = state.copyWith(cards: updatedCards);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to upgrade card: ${e.toString()}');
    }
  }

  Future<void> toggleFavorite(int playerCardId) async {
    try {
      final response = await ApiService.instance.toggleCardFavorite(playerCardId);
      
      if (response.isSuccess && response.data != null) {
        final isFavorite = response.data!;
        final updatedCards = state.cards.map((card) {
          return card.playerCardId == playerCardId 
            ? card.copyWith(isFavorite: isFavorite)
            : card;
        }).toList();
        
        state = state.copyWith(cards: updatedCards);
      }
    } catch (e) {
      state = state.copyWith(error: 'Failed to toggle favorite: ${e.toString()}');
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Card Catalog State
class CardCatalogState {
  final List<GameCard> cards;
  final bool isLoading;
  final String? error;

  const CardCatalogState({
    this.cards = const [],
    this.isLoading = false,
    this.error,
  });

  CardCatalogState copyWith({
    List<GameCard>? cards,
    bool? isLoading,
    String? error,
  }) {
    return CardCatalogState(
      cards: cards ?? this.cards,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

// Card Catalog Notifier
class CardCatalogNotifier extends StateNotifier<CardCatalogState> {
  CardCatalogNotifier() : super(const CardCatalogState());

  Future<void> loadCatalog({String? type, String? rarity}) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final response = await ApiService.instance.getCardCatalog(
        type: type,
        rarity: rarity,
      );
      
      if (response.isSuccess && response.data != null) {
        state = state.copyWith(
          cards: response.data!,
          isLoading: false,
        );
      } else {
        state = state.copyWith(
          error: response.error ?? 'Failed to load card catalog',
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Failed to load card catalog: ${e.toString()}',
        isLoading: false,
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Card Filter
class CardFilter {
  final String? type;
  final String? rarity;
  final bool favoritesOnly;
  final String searchQuery;
  final CardSortBy sortBy;

  const CardFilter({
    this.type,
    this.rarity,
    this.favoritesOnly = false,
    this.searchQuery = '',
    this.sortBy = CardSortBy.dateObtained,
  });

  CardFilter copyWith({
    String? type,
    String? rarity,
    bool? favoritesOnly,
    String? searchQuery,
    CardSortBy? sortBy,
  }) {
    return CardFilter(
      type: type,
      rarity: rarity,
      favoritesOnly: favoritesOnly ?? this.favoritesOnly,
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
    );
  }
}

// Card Filter Notifier
class CardFilterNotifier extends StateNotifier<CardFilter> {
  CardFilterNotifier() : super(const CardFilter());

  void setType(String? type) {
    state = state.copyWith(type: type);
  }

  void setRarity(String? rarity) {
    state = state.copyWith(rarity: rarity);
  }

  void setFavoritesOnly(bool favoritesOnly) {
    state = state.copyWith(favoritesOnly: favoritesOnly);
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  void setSortBy(CardSortBy sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void clearFilters() {
    state = const CardFilter();
  }
}

// Sort options
enum CardSortBy {
  name,
  rarity,
  level,
  dateObtained,
}

extension CardSortByExtension on CardSortBy {
  String get label {
    switch (this) {
      case CardSortBy.name:
        return 'Name';
      case CardSortBy.rarity:
        return 'Rarity';
      case CardSortBy.level:
        return 'Level';
      case CardSortBy.dateObtained:
        return 'Date Obtained';
    }
  }
}
