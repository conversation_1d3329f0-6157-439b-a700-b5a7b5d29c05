import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../models/user.dart';
import '../services/auth_service.dart';
import '../services/api_service.dart';

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) => AuthService());

// Auth state provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier(ref.read(authServiceProvider));
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

// Auth loading provider
final authLoadingProvider = Provider<bool>((ref) {
  final authState = ref.watch(authProvider);
  return authState.isLoading;
});

// Auth error provider
final authErrorProvider = Provider<String?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.error;
});

// Auth state class
class AuthState {
  final User? user;
  final bool isLoading;
  final String? error;
  final bool isAuthenticated;

  const AuthState({
    this.user,
    this.isLoading = false,
    this.error,
    this.isAuthenticated = false,
  });

  AuthState copyWith({
    User? user,
    bool? isLoading,
    String? error,
    bool? isAuthenticated,
  }) {
    return AuthState(
      user: user ?? this.user,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
    );
  }
}

// Auth state notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthNotifier(this._authService) : super(const AuthState()) {
    _initializeAuth();
  }

  // Initialize authentication state
  Future<void> _initializeAuth() async {
    state = state.copyWith(isLoading: true);
    
    try {
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        final user = await _authService.getCurrentUser();
        if (user != null) {
          state = state.copyWith(
            user: user,
            isAuthenticated: true,
            isLoading: false,
          );
          return;
        }
      }
    } catch (e) {
      // If initialization fails, continue to logged out state
    }
    
    state = state.copyWith(
      isLoading: false,
      isAuthenticated: false,
    );
  }

  // Login
  Future<bool> login(String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _authService.login(email, password);
      
      if (result.isSuccess && result.user != null) {
        state = state.copyWith(
          user: result.user,
          isAuthenticated: true,
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          error: result.error,
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Login failed: ${e.toString()}',
        isLoading: false,
      );
      return false;
    }
  }

  // Register
  Future<bool> register(String username, String email, String password) async {
    state = state.copyWith(isLoading: true, error: null);
    
    try {
      final result = await _authService.register(username, email, password);
      
      if (result.isSuccess && result.user != null) {
        state = state.copyWith(
          user: result.user,
          isAuthenticated: true,
          isLoading: false,
        );
        return true;
      } else {
        state = state.copyWith(
          error: result.error,
          isLoading: false,
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        error: 'Registration failed: ${e.toString()}',
        isLoading: false,
      );
      return false;
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    if (!state.isAuthenticated) return;
    
    try {
      final result = await _authService.refreshUserData();
      
      if (result.isSuccess && result.user != null) {
        state = state.copyWith(user: result.user);
      }
    } catch (e) {
      // Silently fail refresh - user can still use cached data
    }
  }

  // Update user data (after purchases, upgrades, etc.)
  void updateUser(User user) {
    if (state.isAuthenticated) {
      state = state.copyWith(user: user);
    }
  }

  // Update user coins
  void updateCoins(int coins) {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(coins: coins);
      state = state.copyWith(user: updatedUser);
    }
  }

  // Update user gems
  void updateGems(int gems) {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(gems: gems);
      state = state.copyWith(user: updatedUser);
    }
  }

  // Update user level and XP
  void updateLevelAndXP(int level, int xp) {
    if (state.user != null) {
      final updatedUser = state.user!.copyWith(level: level, xp: xp);
      state = state.copyWith(user: updatedUser);
    }
  }

  // Logout
  Future<void> logout() async {
    state = state.copyWith(isLoading: true);
    
    try {
      await _authService.logout();
    } catch (e) {
      // Continue with logout even if API call fails
    }
    
    state = const AuthState(
      isLoading: false,
      isAuthenticated: false,
    );
  }

  // Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  // Validate login form
  Map<String, String?> validateLoginForm(String email, String password) {
    return {
      'email': _authService.validateEmail(email),
      'password': _authService.validatePassword(password),
    };
  }

  // Validate registration form
  Map<String, String?> validateRegistrationForm(
    String username,
    String email,
    String password,
    String confirmPassword,
  ) {
    final errors = <String, String?>{
      'username': _authService.validateUsername(username),
      'email': _authService.validateEmail(email),
      'password': _authService.validatePassword(password),
    };

    // Check password confirmation
    if (password != confirmPassword) {
      errors['confirmPassword'] = 'Passwords do not match';
    }

    return errors;
  }

  // Get password strength
  PasswordStrength getPasswordStrength(String password) {
    return _authService.getPasswordStrength(password);
  }
}
