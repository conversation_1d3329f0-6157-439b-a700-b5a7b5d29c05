-- Rally Events for Rally Championship Manager

-- Bronze Championship Events
INSERT INTO rally_events (name, championship_tier, stages, entry_cost_coins, entry_cost_gems, rewards) VALUES
('Forest Sprint Championship', 'bronze',
 '[
   {"name": "Pine Valley Sprint", "type": "sprint", "surface": "gravel", "length": 3.2, "difficulty": 1, "weather": "clear"},
   {"name": "Woodland Rush", "type": "technical", "surface": "gravel", "length": 4.1, "difficulty": 2, "weather": "clear"},
   {"name": "Forest Finale", "type": "high_speed", "surface": "gravel", "length": 5.8, "difficulty": 2, "weather": "clear"}
 ]',
 50, 0,
 '{"championship_points": [15, 12, 10, 8, 6, 5, 4, 3, 2, 1], "coins": [100, 80, 60, 50, 40, 30, 25, 20, 15, 10], "xp": 50}'),

('City Circuit Rally', 'bronze',
 '[
   {"name": "Downtown Dash", "type": "sprint", "surface": "tarmac", "length": 2.8, "difficulty": 1, "weather": "clear"},
   {"name": "Harbor Bridge", "type": "technical", "surface": "tarmac", "length": 3.5, "difficulty": 2, "weather": "clear"},
   {"name": "Industrial Zone", "type": "mixed_surface", "surface": "mixed", "length": 4.2, "difficulty": 2, "weather": "clear"},
   {"name": "City Center Sprint", "type": "high_speed", "surface": "tarmac", "length": 3.9, "difficulty": 1, "weather": "clear"}
 ]',
 75, 0,
 '{"championship_points": [20, 16, 13, 11, 9, 8, 7, 6, 5, 4], "coins": [150, 120, 90, 75, 60, 45, 35, 25, 20, 15], "xp": 75}');

-- Silver Championship Events  
INSERT INTO rally_events (name, championship_tier, stages, entry_cost_coins, entry_cost_gems, rewards) VALUES
('Mountain Challenge', 'silver',
 '[
   {"name": "Alpine Ascent", "type": "technical", "surface": "tarmac", "length": 6.2, "difficulty": 3, "weather": "clear"},
   {"name": "Peak Performance", "type": "high_speed", "surface": "tarmac", "length": 8.1, "difficulty": 3, "weather": "fog"},
   {"name": "Cliffside Danger", "type": "extreme", "surface": "gravel", "length": 7.5, "difficulty": 4, "weather": "rain"},
   {"name": "Summit Sprint", "type": "sprint", "surface": "gravel", "length": 4.8, "difficulty": 3, "weather": "clear"},
   {"name": "Descent Madness", "type": "high_speed", "surface": "mixed", "length": 9.2, "difficulty": 4, "weather": "clear"}
 ]',
 150, 0,
 '{"championship_points": [25, 20, 16, 13, 11, 10, 9, 8, 7, 6], "coins": [250, 200, 150, 125, 100, 80, 65, 50, 40, 30], "xp": 100}'),

('Desert Storm Rally', 'silver',
 '[
   {"name": "Dune Crossing", "type": "high_speed", "surface": "gravel", "length": 12.3, "difficulty": 3, "weather": "clear"},
   {"name": "Oasis Sprint", "type": "sprint", "surface": "gravel", "length": 5.1, "difficulty": 2, "weather": "clear"},
   {"name": "Sandstorm Survival", "type": "extreme", "surface": "gravel", "length": 8.7, "difficulty": 5, "weather": "fog"},
   {"name": "Canyon Run", "type": "technical", "surface": "gravel", "length": 7.9, "difficulty": 4, "weather": "clear"},
   {"name": "Mirage Finish", "type": "mixed_surface", "surface": "mixed", "length": 6.4, "difficulty": 3, "weather": "clear"}
 ]',
 200, 0,
 '{"championship_points": [25, 20, 16, 13, 11, 10, 9, 8, 7, 6], "coins": [300, 240, 180, 150, 120, 95, 75, 60, 45, 35], "xp": 125}');

-- Gold Championship Events
INSERT INTO rally_events (name, championship_tier, stages, entry_cost_coins, entry_cost_gems, rewards) VALUES
('Arctic Expedition', 'gold',
 '[
   {"name": "Frozen Lake", "type": "high_speed", "surface": "snow", "length": 15.2, "difficulty": 4, "weather": "snow"},
   {"name": "Ice Road Truckers", "type": "extreme", "surface": "snow", "length": 11.8, "difficulty": 5, "weather": "snow"},
   {"name": "Blizzard Blast", "type": "technical", "surface": "snow", "length": 8.9, "difficulty": 5, "weather": "snow"},
   {"name": "Aurora Sprint", "type": "sprint", "surface": "snow", "length": 6.3, "difficulty": 4, "weather": "clear"},
   {"name": "Polar Plunge", "type": "extreme", "surface": "snow", "length": 13.7, "difficulty": 6, "weather": "fog"},
   {"name": "Midnight Sun", "type": "high_speed", "surface": "snow", "length": 18.1, "difficulty": 5, "weather": "clear"}
 ]',
 0, 25,
 '{"championship_points": [30, 24, 19, 16, 14, 13, 12, 11, 10, 9], "coins": [500, 400, 300, 250, 200, 160, 130, 100, 80, 60], "xp": 200, "card_pack": "silver"}'),

('Night Rally Championship', 'gold',
 '[
   {"name": "Moonlight Sprint", "type": "sprint", "surface": "tarmac", "length": 4.7, "difficulty": 4, "weather": "clear", "time": "night"},
   {"name": "Shadow Valley", "type": "technical", "surface": "gravel", "length": 9.3, "difficulty": 5, "weather": "fog", "time": "night"},
   {"name": "Starlight Express", "type": "high_speed", "surface": "tarmac", "length": 16.8, "difficulty": 4, "weather": "clear", "time": "night"},
   {"name": "Midnight Madness", "type": "extreme", "surface": "mixed", "length": 12.1, "difficulty": 6, "weather": "rain", "time": "night"},
   {"name": "Dawn Breaker", "type": "high_speed", "surface": "gravel", "length": 14.5, "difficulty": 5, "weather": "clear", "time": "dawn"}
 ]',
 0, 30,
 '{"championship_points": [30, 24, 19, 16, 14, 13, 12, 11, 10, 9], "coins": [600, 480, 360, 300, 240, 190, 150, 120, 95, 75], "xp": 250, "card_pack": "gold"}');

-- Elite Championship Events
INSERT INTO rally_events (name, championship_tier, stages, entry_cost_coins, entry_cost_gems, rewards) VALUES
('World Championship Final', 'elite',
 '[
   {"name": "Monte Carlo Classic", "type": "extreme", "surface": "mixed", "length": 22.4, "difficulty": 7, "weather": "rain", "time": "night"},
   {"name": "Finnish Flying", "type": "high_speed", "surface": "gravel", "length": 28.7, "difficulty": 6, "weather": "clear"},
   {"name": "Welsh Dragon", "type": "technical", "surface": "gravel", "length": 19.3, "difficulty": 7, "weather": "fog"},
   {"name": "Swedish Snow", "type": "extreme", "surface": "snow", "length": 25.1, "difficulty": 8, "weather": "snow"},
   {"name": "German Precision", "type": "technical", "surface": "tarmac", "length": 21.8, "difficulty": 6, "weather": "clear"},
   {"name": "Safari Survival", "type": "extreme", "surface": "gravel", "length": 31.2, "difficulty": 8, "weather": "clear"},
   {"name": "Championship Decider", "type": "mixed_surface", "surface": "mixed", "length": 26.9, "difficulty": 7, "weather": "rain"}
 ]',
 0, 50,
 '{"championship_points": [50, 40, 32, 26, 22, 20, 18, 16, 14, 12], "coins": [1000, 800, 600, 500, 400, 320, 250, 200, 160, 120], "xp": 500, "card_pack": "legendary", "gems": 100}');
