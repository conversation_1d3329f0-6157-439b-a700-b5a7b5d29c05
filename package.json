{"name": "rally-championship-manager-api", "version": "1.0.0", "description": "Backend API for Rally Championship Manager card game", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["rally", "card-game", "api", "nodejs"], "author": "RallyForge Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "socket.io": "^4.7.4", "uuid": "^9.0.1", "joi": "^17.11.0", "dotenv": "^16.3.1", "multer": "^1.4.5-lts.1"}, "devDependencies": {"nodemon": "^3.0.2"}}