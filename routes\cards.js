const express = require('express');
const { authenticateToken } = require('../middleware/auth');
const { query } = require('../database/connection');
const { Card, PlayerCard } = require('../models/Card');
const Player = require('../models/Player');

const router = express.Router();

// Get player's card collection
router.get('/collection', authenticateToken, async (req, res) => {
  try {
    const result = await query(`
      SELECT 
        pc.id as player_card_id,
        pc.level,
        pc.experience,
        pc.obtained_at,
        pc.is_favorite,
        cd.id as card_id,
        cd.name,
        cd.type,
        cd.rarity,
        cd.stats,
        cd.abilities,
        cd.image_url,
        cd.description
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1
      ORDER BY cd.rarity DESC, cd.name ASC
    `, [req.user.id]);

    res.json({ cards: result.rows });
  } catch (error) {
    console.error('Get card collection error:', error);
    res.status(500).json({ error: 'Failed to fetch card collection' });
  }
});

// Get all available cards (for reference)
router.get('/catalog', async (req, res) => {
  try {
    const { type, rarity } = req.query;
    let whereClause = 'WHERE is_active = true';
    const params = [];
    let paramCount = 1;

    if (type) {
      whereClause += ` AND type = $${paramCount}`;
      params.push(type);
      paramCount++;
    }

    if (rarity) {
      whereClause += ` AND rarity = $${paramCount}`;
      params.push(rarity);
      paramCount++;
    }

    const result = await query(`
      SELECT
        id,
        name,
        type,
        rarity,
        stats,
        abilities,
        image_url,
        description,
        flavor_text
      FROM card_definitions
      ${whereClause}
      ORDER BY rarity DESC, name ASC
    `, params);

    res.json({ cards: result.rows });
  } catch (error) {
    console.error('Get card catalog error:', error);
    res.status(500).json({ error: 'Failed to fetch card catalog' });
  }
});

// Upgrade player card
router.post('/upgrade/:playerCardId', authenticateToken, async (req, res) => {
  try {
    const { playerCardId } = req.params;

    // Get player card
    const playerCard = await PlayerCard.findById(playerCardId);
    if (!playerCard) {
      return res.status(404).json({ error: 'Card not found' });
    }

    // Verify ownership
    if (playerCard.playerId !== req.user.id) {
      return res.status(403).json({ error: 'Card not owned by player' });
    }

    // Calculate upgrade cost (simplified)
    const upgradeCost = playerCard.level * 100;

    // Get player
    const player = await Player.findById(req.user.id);
    if (player.coins < upgradeCost) {
      return res.status(400).json({ error: 'Insufficient coins for upgrade' });
    }

    // Deduct coins
    const success = await player.spendCoins(upgradeCost);
    if (!success) {
      return res.status(400).json({ error: 'Failed to deduct upgrade cost' });
    }

    // Add experience (100 XP per upgrade)
    const upgradeResult = await playerCard.addExperience(100);

    res.json({
      success: true,
      playerCard: playerCard.toJSON(),
      upgradeCost,
      leveledUp: upgradeResult.leveledUp,
      newLevel: upgradeResult.newLevel,
      playerCoins: player.coins
    });

  } catch (error) {
    console.error('Upgrade card error:', error);
    res.status(500).json({ error: 'Failed to upgrade card' });
  }
});

// Toggle favorite status
router.post('/favorite/:playerCardId', authenticateToken, async (req, res) => {
  try {
    const { playerCardId } = req.params;

    // Get player card
    const playerCard = await PlayerCard.findById(playerCardId);
    if (!playerCard) {
      return res.status(404).json({ error: 'Card not found' });
    }

    // Verify ownership
    if (playerCard.playerId !== req.user.id) {
      return res.status(403).json({ error: 'Card not owned by player' });
    }

    // Toggle favorite
    await playerCard.toggleFavorite();

    res.json({
      success: true,
      isFavorite: playerCard.isFavorite
    });

  } catch (error) {
    console.error('Toggle favorite error:', error);
    res.status(500).json({ error: 'Failed to toggle favorite' });
  }
});

// Get player's deck configurations
router.get('/decks', authenticateToken, async (req, res) => {
  try {
    const result = await query(`
      SELECT
        pd.*,
        d.name as driver_name,
        cd.name as codriver_name,
        v.name as vehicle_name
      FROM player_decks pd
      LEFT JOIN player_cards pc_d ON pd.driver_card_id = pc_d.id
      LEFT JOIN card_definitions d ON pc_d.card_id = d.id
      LEFT JOIN player_cards pc_cd ON pd.codriver_card_id = pc_cd.id
      LEFT JOIN card_definitions cd ON pc_cd.card_id = cd.id
      LEFT JOIN player_cards pc_v ON pd.vehicle_card_id = pc_v.id
      LEFT JOIN card_definitions v ON pc_v.card_id = v.id
      WHERE pd.player_id = $1 AND pd.is_active = true
      ORDER BY pd.created_at DESC
    `, [req.user.id]);

    res.json({ decks: result.rows });
  } catch (error) {
    console.error('Get decks error:', error);
    res.status(500).json({ error: 'Failed to fetch decks' });
  }
});

// Create new deck configuration
router.post('/decks', authenticateToken, async (req, res) => {
  try {
    const { name, driverId, codriverId, vehicleId, strategyCards = [] } = req.body;

    if (!name || !driverId || !codriverId || !vehicleId) {
      return res.status(400).json({ error: 'Deck name and all card IDs are required' });
    }

    // Verify all cards are owned by player
    const cardCheck = await query(`
      SELECT pc.id, cd.type
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.id IN ($1, $2, $3) AND pc.player_id = $4
    `, [driverId, codriverId, vehicleId, req.user.id]);

    if (cardCheck.rows.length !== 3) {
      return res.status(400).json({ error: 'Some cards not found or not owned' });
    }

    // Verify card types
    const cardTypes = cardCheck.rows.map(row => row.type);
    if (!cardTypes.includes('driver') || !cardTypes.includes('codriver') || !cardTypes.includes('vehicle')) {
      return res.status(400).json({ error: 'Invalid card types for deck' });
    }

    // Create deck
    const result = await query(`
      INSERT INTO player_decks (player_id, name, driver_card_id, codriver_card_id, vehicle_card_id, strategy_cards)
      VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [req.user.id, name, driverId, codriverId, vehicleId, JSON.stringify(strategyCards)]);

    res.json({
      success: true,
      deck: result.rows[0]
    });

  } catch (error) {
    console.error('Create deck error:', error);
    res.status(500).json({ error: 'Failed to create deck' });
  }
});

// Get card statistics
router.get('/stats', authenticateToken, async (req, res) => {
  try {
    const stats = await query(`
      SELECT
        cd.type,
        cd.rarity,
        COUNT(*) as count,
        AVG(pc.level) as avg_level
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1
      GROUP BY cd.type, cd.rarity
      ORDER BY cd.type,
        CASE cd.rarity
          WHEN 'legendary' THEN 1
          WHEN 'epic' THEN 2
          WHEN 'rare' THEN 3
          WHEN 'common' THEN 4
        END
    `, [req.user.id]);

    const totalCards = await query(`
      SELECT COUNT(*) as total FROM player_cards WHERE player_id = $1
    `, [req.user.id]);

    const favoriteCards = await query(`
      SELECT COUNT(*) as total FROM player_cards WHERE player_id = $1 AND is_favorite = true
    `, [req.user.id]);

    res.json({
      cardStats: stats.rows,
      totalCards: parseInt(totalCards.rows[0].total),
      favoriteCards: parseInt(favoriteCards.rows[0].total)
    });

  } catch (error) {
    console.error('Get card stats error:', error);
    res.status(500).json({ error: 'Failed to fetch card statistics' });
  }
});

module.exports = router;
