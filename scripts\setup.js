const { testConnection } = require('../database/connection');
const achievementService = require('../services/achievementService');

async function setupGame() {
  console.log('🚀 Setting up Rally Championship Manager...');
  
  try {
    // Test database connection
    console.log('📊 Testing database connection...');
    const isConnected = await testConnection();
    if (!isConnected) {
      console.error('❌ Database connection failed');
      process.exit(1);
    }

    // Initialize achievements
    console.log('🏆 Initializing achievements...');
    await achievementService.initializeAchievements();

    console.log('✅ Game setup completed successfully!');
    console.log('\n🎮 Rally Championship Manager is ready to play!');
    console.log('📋 Available features:');
    console.log('  • Player registration and authentication');
    console.log('  • Card collection with 29 unique cards');
    console.log('  • Rally simulation with realistic physics');
    console.log('  • Shop with card packs and currency');
    console.log('  • Achievement system with rewards');
    console.log('  • Deck building and management');
    console.log('  • Leaderboards and statistics');

  } catch (error) {
    console.error('❌ Setup failed:', error);
    process.exit(1);
  }

  process.exit(0);
}

// Run setup if called directly
if (require.main === module) {
  setupGame();
}

module.exports = { setupGame };
