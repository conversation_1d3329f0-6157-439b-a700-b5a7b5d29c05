import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:confetti/confetti.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../models/card.dart';
import '../../providers/deck_provider.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import 'rally_results_screen.dart';

class RallySimulationScreen extends ConsumerStatefulWidget {
  final Map<String, dynamic> rallyInfo;
  final Deck deck;

  const RallySimulationScreen({
    super.key,
    required this.rallyInfo,
    required this.deck,
  });

  @override
  ConsumerState<RallySimulationScreen> createState() => _RallySimulationScreenState();
}

class _RallySimulationScreenState extends ConsumerState<RallySimulationScreen>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _carController;
  late AnimationController _backgroundController;
  late ConfettiController _confettiController;

  late Animation<double> _progressAnimation;
  late Animation<double> _carBounceAnimation;
  late Animation<Color?> _backgroundColorAnimation;

  int _currentStage = 0;
  int _totalStages = 5;
  bool _isSimulating = false;
  bool _isComplete = false;
  RallyResult? _result;

  final List<String> _stageNames = [
    'Forest Sprint',
    'Mountain Climb',
    'Desert Dash',
    'City Circuit',
    'Final Challenge',
  ];

  @override
  void initState() {
    super.initState();
    _totalStages = widget.rallyInfo['stages'] ?? 5;
    _initializeAnimations();
    _startSimulation();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _carController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _backgroundController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _confettiController = ConfettiController(
      duration: const Duration(seconds: 3),
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _carBounceAnimation = Tween<double>(
      begin: 0.0,
      end: 10.0,
    ).animate(CurvedAnimation(
      parent: _carController,
      curve: Curves.elasticOut,
    ));

    _backgroundColorAnimation = ColorTween(
      begin: AppTheme.darkBackground,
      end: AppTheme.primaryColor.withOpacity(0.1),
    ).animate(_backgroundController);

    // Start car bounce animation
    _carController.repeat(reverse: true);
  }

  void _startSimulation() async {
    setState(() {
      _isSimulating = true;
    });

    // Simulate each stage
    for (int i = 0; i < _totalStages; i++) {
      setState(() {
        _currentStage = i;
      });

      // Animate progress for this stage
      _progressController.reset();
      await _progressController.forward();

      // Brief pause between stages
      await Future.delayed(const Duration(milliseconds: 500));
    }

    // Complete the rally
    await _completeRally();
  }

  Future<void> _completeRally() async {
    setState(() {
      _isComplete = true;
    });

    // Stop car animation and start background animation
    _carController.stop();
    _backgroundController.forward();

    // Get rally result
    final result = await ref.read(rallyParticipationProvider.notifier).joinRally(
      widget.rallyInfo['id']?.toString() ?? 'rally_1',
      widget.deck,
    );

    if (result != null) {
      setState(() {
        _result = result;
      });

      // Update user rewards
      final user = ref.read(currentUserProvider);
      if (user != null) {
        ref.read(authProvider.notifier).updateCoins(user.coins + result.coinsEarned);
        // TODO: Update XP when user model supports it
      }

      // Show confetti for good results
      if (result.position <= 3) {
        _confettiController.play();
      }

      // Auto-navigate to results after a delay
      Future.delayed(const Duration(seconds: 3), () {
        if (mounted) {
          _navigateToResults();
        }
      });
    }

    setState(() {
      _isSimulating = false;
    });
  }

  void _navigateToResults() {
    if (_result != null) {
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(
          builder: (context) => RallyResultsScreen(
            rallyInfo: widget.rallyInfo,
            deck: widget.deck,
            result: _result!,
          ),
        ),
      );
    }
  }

  @override
  void dispose() {
    _progressController.dispose();
    _carController.dispose();
    _backgroundController.dispose();
    _confettiController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: AnimatedBuilder(
        animation: _backgroundController,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  _backgroundColorAnimation.value ?? AppTheme.darkBackground,
                  AppTheme.darkBackground,
                ],
              ),
            ),
            child: Stack(
              children: [
                // Main content
                SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.all(AppConstants.defaultPadding),
                    child: Column(
                      children: [
                        // Header
                        _buildHeader(),
                        
                        const SizedBox(height: 40),
                        
                        // Rally car animation
                        _buildCarAnimation(),
                        
                        const SizedBox(height: 40),
                        
                        // Progress section
                        _buildProgressSection(),
                        
                        const Spacer(),
                        
                        // Current stage info
                        _buildCurrentStageInfo(),
                        
                        const SizedBox(height: 40),
                        
                        // Results or skip button
                        _buildActionButton(),
                      ],
                    ),
                  ),
                ),
                
                // Confetti
                Align(
                  alignment: Alignment.topCenter,
                  child: ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirectionality: BlastDirectionality.explosive,
                    shouldLoop: false,
                    colors: const [
                      AppTheme.primaryColor,
                      AppTheme.accentColor,
                      AppTheme.successColor,
                      AppTheme.warningColor,
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.rallyInfo['name'] ?? 'Rally Event',
                style: AppTheme.headingMedium.copyWith(color: Colors.white),
              ),
              Text(
                'Racing in progress...',
                style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
              ),
            ],
          ),
        ),
        if (!_isComplete)
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: const Icon(Icons.close, color: Colors.white70),
          ),
      ],
    );
  }

  Widget _buildCarAnimation() {
    return AnimatedBuilder(
      animation: _carBounceAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(0, -_carBounceAnimation.value),
          child: Container(
            width: 120,
            height: 80,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [AppTheme.primaryColor, AppTheme.accentColor],
              ),
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppTheme.primaryColor.withOpacity(0.3),
                  blurRadius: 15,
                  spreadRadius: 2,
                  offset: Offset(0, _carBounceAnimation.value + 5),
                ),
              ],
            ),
            child: const Icon(
              Icons.directions_car,
              color: Colors.white,
              size: 48,
            ),
          ),
        );
      },
    );
  }

  Widget _buildProgressSection() {
    return Column(
      children: [
        Text(
          'Stage ${_currentStage + 1} of $_totalStages',
          style: AppTheme.headingSmall.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        
        // Overall progress bar
        Container(
          height: 8,
          decoration: BoxDecoration(
            color: Colors.white12,
            borderRadius: BorderRadius.circular(4),
          ),
          child: AnimatedBuilder(
            animation: _progressAnimation,
            builder: (context, child) {
              final overallProgress = (_currentStage + _progressAnimation.value) / _totalStages;
              return FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: overallProgress,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [AppTheme.primaryColor, AppTheme.accentColor],
                    ),
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              );
            },
          ),
        ),
        
        const SizedBox(height: 8),
        
        // Progress percentage
        AnimatedBuilder(
          animation: _progressAnimation,
          builder: (context, child) {
            final overallProgress = (_currentStage + _progressAnimation.value) / _totalStages;
            return Text(
              '${(overallProgress * 100).toInt()}%',
              style: AppTheme.bodyMedium.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildCurrentStageInfo() {
    if (_isComplete && _result != null) {
      return _buildResultsPreview();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(color: Colors.white12),
      ),
      child: Column(
        children: [
          Text(
            _currentStage < _stageNames.length 
              ? _stageNames[_currentStage]
              : 'Stage ${_currentStage + 1}',
            style: AppTheme.headingSmall.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 8),
          Text(
            _isSimulating 
              ? 'Racing through the course...'
              : 'Preparing for next stage...',
            style: AppTheme.bodyMedium.copyWith(color: Colors.white70),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultsPreview() {
    if (_result == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: _result!.position <= 3 
            ? AppTheme.successColor.withOpacity(0.3)
            : Colors.white12,
        ),
      ),
      child: Column(
        children: [
          Text(
            'Rally Complete!',
            style: AppTheme.headingSmall.copyWith(color: Colors.white),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildResultItem('Position', '${_result!.position}${_result!.positionSuffix}'),
              _buildResultItem('Time', _result!.formattedTime),
              _buildResultItem('Coins', '+${_result!.coinsEarned}'),
            ],
          ),
          if (_result!.isPersonalBest) ...[
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                '🏆 Personal Best!',
                style: AppTheme.bodySmall.copyWith(
                  color: AppTheme.successColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
    ).animate().scale(
      begin: const Offset(0.8, 0.8),
      end: const Offset(1.0, 1.0),
      duration: 500.ms,
      curve: Curves.elasticOut,
    );
  }

  Widget _buildResultItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: AppTheme.bodyMedium.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: AppTheme.bodySmall.copyWith(
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton() {
    if (_isComplete && _result != null) {
      return SizedBox(
        width: double.infinity,
        child: PrimaryButton(
          text: 'View Results',
          onPressed: _navigateToResults,
          icon: Icons.emoji_events,
        ),
      );
    }

    if (_isSimulating) {
      return SizedBox(
        width: double.infinity,
        child: SecondaryButton(
          text: 'Skip Animation',
          onPressed: () {
            // Skip to results
            _progressController.stop();
            _carController.stop();
            _completeRally();
          },
        ),
      );
    }

    return const SizedBox.shrink();
  }
}
