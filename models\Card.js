const { query } = require('../database/connection');
const { CARD_TYPES, CARD_RARITIES } = require('../utils/constants');

class Card {
  constructor(data) {
    this.id = data.id;
    this.name = data.name;
    this.type = data.type;
    this.rarity = data.rarity;
    this.stats = data.stats;
    this.abilities = data.abilities;
    this.imageUrl = data.image_url;
    this.description = data.description;
    this.flavorText = data.flavor_text;
    this.isActive = data.is_active;
    this.createdAt = data.created_at;
  }

  // Find card by ID
  static async findById(id) {
    const result = await query(
      'SELECT * FROM card_definitions WHERE id = $1 AND is_active = true',
      [id]
    );
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new Card(result.rows[0]);
  }

  // Get all cards by type
  static async findByType(type) {
    const result = await query(
      'SELECT * FROM card_definitions WHERE type = $1 AND is_active = true ORDER BY rarity DESC, name ASC',
      [type]
    );
    
    return result.rows.map(row => new Card(row));
  }

  // Get all cards by rarity
  static async findByRarity(rarity) {
    const result = await query(
      'SELECT * FROM card_definitions WHERE rarity = $1 AND is_active = true ORDER BY name ASC',
      [rarity]
    );
    
    return result.rows.map(row => new Card(row));
  }

  // Get all active cards
  static async findAll() {
    const result = await query(
      'SELECT * FROM card_definitions WHERE is_active = true ORDER BY rarity DESC, type, name ASC'
    );
    
    return result.rows.map(row => new Card(row));
  }

  // Create new card definition
  static async create(cardData) {
    const { name, type, rarity, stats, abilities, imageUrl, description, flavorText } = cardData;
    
    const result = await query(
      `INSERT INTO card_definitions (name, type, rarity, stats, abilities, image_url, description, flavor_text) 
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
       RETURNING *`,
      [name, type, rarity, JSON.stringify(stats), JSON.stringify(abilities), imageUrl, description, flavorText]
    );
    
    return new Card(result.rows[0]);
  }

  // Get random cards by rarity (for pack opening)
  static async getRandomByRarity(rarity, count = 1, excludeIds = []) {
    let excludeClause = '';
    let params = [rarity, count];
    
    if (excludeIds.length > 0) {
      excludeClause = `AND id NOT IN (${excludeIds.map((_, i) => `$${i + 3}`).join(', ')})`;
      params = [...params, ...excludeIds];
    }
    
    const result = await query(
      `SELECT * FROM card_definitions 
       WHERE rarity = $1 AND is_active = true ${excludeClause}
       ORDER BY RANDOM() 
       LIMIT $2`,
      params
    );
    
    return result.rows.map(row => new Card(row));
  }

  // Update card definition
  async update(updates) {
    const allowedFields = ['name', 'stats', 'abilities', 'image_url', 'description', 'flavor_text', 'is_active'];
    const updateFields = [];
    const values = [];
    let paramCount = 1;

    for (const [field, value] of Object.entries(updates)) {
      if (allowedFields.includes(field)) {
        const dbField = field === 'imageUrl' ? 'image_url' : 
                       field === 'flavorText' ? 'flavor_text' : 
                       field === 'isActive' ? 'is_active' : field;
        
        updateFields.push(`${dbField} = $${paramCount}`);
        
        // JSON stringify objects
        if (field === 'stats' || field === 'abilities') {
          values.push(JSON.stringify(value));
        } else {
          values.push(value);
        }
        paramCount++;
      }
    }

    if (updateFields.length === 0) {
      return this;
    }

    values.push(this.id);
    const result = await query(
      `UPDATE card_definitions SET ${updateFields.join(', ')} WHERE id = $${paramCount} RETURNING *`,
      values
    );

    if (result.rows.length > 0) {
      Object.assign(this, result.rows[0]);
    }

    return this;
  }

  // Calculate effective stats at a given level
  getEffectiveStats(level = 1) {
    if (!this.stats || level <= 1) {
      return this.stats;
    }

    const effectiveStats = { ...this.stats };
    const levelBonus = (level - 1) * 0.05; // 5% increase per level

    for (const [stat, value] of Object.entries(effectiveStats)) {
      if (typeof value === 'number') {
        effectiveStats[stat] = Math.round(value * (1 + levelBonus));
      }
    }

    return effectiveStats;
  }

  // Check if card has specific ability
  hasAbility(abilityName) {
    return this.abilities && this.abilities[abilityName] !== undefined;
  }

  // Get ability value
  getAbilityValue(abilityName) {
    return this.abilities && this.abilities[abilityName];
  }

  // Deactivate card
  async deactivate() {
    await this.update({ isActive: false });
    return this;
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      name: this.name,
      type: this.type,
      rarity: this.rarity,
      stats: this.stats,
      abilities: this.abilities,
      imageUrl: this.imageUrl,
      description: this.description,
      flavorText: this.flavorText,
      isActive: this.isActive,
      createdAt: this.createdAt
    };
  }
}

// Player Card (instance of a card owned by a player)
class PlayerCard {
  constructor(data) {
    this.id = data.id;
    this.playerId = data.player_id;
    this.cardId = data.card_id;
    this.level = data.level;
    this.experience = data.experience;
    this.obtainedAt = data.obtained_at;
    this.isFavorite = data.is_favorite;
    
    // If card definition is included
    if (data.name) {
      this.card = new Card(data);
    }
  }

  // Find player card by ID
  static async findById(id) {
    const result = await query(`
      SELECT 
        pc.*,
        cd.name, cd.type, cd.rarity, cd.stats, cd.abilities, 
        cd.image_url, cd.description, cd.flavor_text, cd.is_active, cd.created_at
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.id = $1
    `, [id]);
    
    if (result.rows.length === 0) {
      return null;
    }
    
    return new PlayerCard(result.rows[0]);
  }

  // Add card to player's collection
  static async addToPlayer(playerId, cardId) {
    const result = await query(
      'INSERT INTO player_cards (player_id, card_id) VALUES ($1, $2) RETURNING *',
      [playerId, cardId]
    );
    
    return new PlayerCard(result.rows[0]);
  }

  // Get player's cards by type
  static async getPlayerCardsByType(playerId, type) {
    const result = await query(`
      SELECT 
        pc.*,
        cd.name, cd.type, cd.rarity, cd.stats, cd.abilities, 
        cd.image_url, cd.description, cd.flavor_text, cd.is_active, cd.created_at
      FROM player_cards pc
      JOIN card_definitions cd ON pc.card_id = cd.id
      WHERE pc.player_id = $1 AND cd.type = $2
      ORDER BY cd.rarity DESC, cd.name ASC
    `, [playerId, type]);
    
    return result.rows.map(row => new PlayerCard(row));
  }

  // Upgrade card (add experience)
  async addExperience(amount) {
    const newExp = this.experience + amount;
    let newLevel = this.level;
    
    // Simple leveling: every 100 XP = 1 level
    const expPerLevel = 100;
    newLevel = Math.floor(newExp / expPerLevel) + 1;
    
    const result = await query(
      'UPDATE player_cards SET experience = $1, level = $2 WHERE id = $3 RETURNING *',
      [newExp, newLevel, this.id]
    );
    
    this.experience = newExp;
    this.level = newLevel;
    
    return {
      leveledUp: newLevel > this.level,
      previousLevel: this.level,
      newLevel
    };
  }

  // Toggle favorite status
  async toggleFavorite() {
    const result = await query(
      'UPDATE player_cards SET is_favorite = NOT is_favorite WHERE id = $1 RETURNING is_favorite',
      [this.id]
    );
    
    this.isFavorite = result.rows[0].is_favorite;
    return this;
  }

  // Get effective stats (card stats + level bonus)
  getEffectiveStats() {
    if (!this.card) return {};
    return this.card.getEffectiveStats(this.level);
  }

  // Convert to JSON
  toJSON() {
    return {
      id: this.id,
      playerId: this.playerId,
      cardId: this.cardId,
      level: this.level,
      experience: this.experience,
      obtainedAt: this.obtainedAt,
      isFavorite: this.isFavorite,
      card: this.card ? this.card.toJSON() : null,
      effectiveStats: this.getEffectiveStats()
    };
  }
}

module.exports = { Card, PlayerCard };
