import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../constants/app_theme.dart';
import '../services/auth_service.dart';

class PasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final PasswordStrength strength;

  const PasswordStrengthIndicator({
    super.key,
    required this.password,
    required this.strength,
  });

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: _buildStrengthBar(),
            ),
            const SizedBox(width: 12),
            _buildStrengthLabel(),
          ],
        ),
        const SizedBox(height: 8),
        _buildRequirements(),
      ],
    );
  }

  Widget _buildStrengthBar() {
    return Container(
      height: 4,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(2),
        color: Colors.white12,
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: strength.progress,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(2),
            color: _getStrengthColor(),
          ),
        ),
      ),
    ).animate().scaleX(
      duration: 300.ms,
      curve: Curves.easeOut,
    );
  }

  Widget _buildStrengthLabel() {
    if (strength == PasswordStrength.none) {
      return const SizedBox.shrink();
    }

    return Text(
      strength.label,
      style: TextStyle(
        fontSize: 12,
        fontWeight: FontWeight.w500,
        color: _getStrengthColor(),
      ),
    ).animate().fadeIn(duration: 200.ms);
  }

  Widget _buildRequirements() {
    final requirements = _getPasswordRequirements();
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: requirements.map((requirement) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 4),
          child: Row(
            children: [
              Icon(
                requirement.isMet ? Icons.check_circle : Icons.radio_button_unchecked,
                size: 16,
                color: requirement.isMet 
                  ? AppTheme.successColor 
                  : Colors.white38,
              ),
              const SizedBox(width: 8),
              Text(
                requirement.text,
                style: TextStyle(
                  fontSize: 12,
                  color: requirement.isMet 
                    ? Colors.white70 
                    : Colors.white54,
                ),
              ),
            ],
          ),
        ).animate().fadeIn(
          delay: (requirements.indexOf(requirement) * 100).ms,
          duration: 200.ms,
        );
      }).toList(),
    );
  }

  Color _getStrengthColor() {
    switch (strength) {
      case PasswordStrength.weak:
        return AppTheme.errorColor;
      case PasswordStrength.medium:
        return AppTheme.warningColor;
      case PasswordStrength.strong:
        return AppTheme.successColor;
      case PasswordStrength.none:
        return Colors.transparent;
    }
  }

  List<PasswordRequirement> _getPasswordRequirements() {
    return [
      PasswordRequirement(
        text: 'At least 6 characters',
        isMet: password.length >= 6,
      ),
      PasswordRequirement(
        text: 'Contains lowercase letter',
        isMet: RegExp(r'[a-z]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains uppercase letter',
        isMet: RegExp(r'[A-Z]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains number',
        isMet: RegExp(r'[0-9]').hasMatch(password),
      ),
      PasswordRequirement(
        text: 'Contains special character',
        isMet: RegExp(r'[!@#$%^&*(),.?":{}|<>]').hasMatch(password),
      ),
    ];
  }
}

class PasswordRequirement {
  final String text;
  final bool isMet;

  const PasswordRequirement({
    required this.text,
    required this.isMet,
  });
}

// Alternative compact version
class CompactPasswordStrengthIndicator extends StatelessWidget {
  final String password;
  final PasswordStrength strength;

  const CompactPasswordStrengthIndicator({
    super.key,
    required this.password,
    required this.strength,
  });

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return const SizedBox.shrink();
    }

    return Row(
      children: [
        Expanded(
          child: Container(
            height: 3,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(1.5),
              color: Colors.white12,
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: strength.progress,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(1.5),
                  color: _getStrengthColor(),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        if (strength != PasswordStrength.none)
          Text(
            strength.label,
            style: TextStyle(
              fontSize: 11,
              fontWeight: FontWeight.w500,
              color: _getStrengthColor(),
            ),
          ),
      ],
    ).animate().fadeIn(duration: 200.ms);
  }

  Color _getStrengthColor() {
    switch (strength) {
      case PasswordStrength.weak:
        return AppTheme.errorColor;
      case PasswordStrength.medium:
        return AppTheme.warningColor;
      case PasswordStrength.strong:
        return AppTheme.successColor;
      case PasswordStrength.none:
        return Colors.transparent;
    }
  }
}
