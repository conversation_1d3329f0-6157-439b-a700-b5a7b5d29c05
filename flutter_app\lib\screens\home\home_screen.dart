import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../constants/app_theme.dart';
import '../../constants/app_constants.dart';
import '../../providers/auth_provider.dart';
import '../../widgets/custom_button.dart';
import '../cards/card_collection_screen.dart';
import '../rally/rally_screen.dart';
import '../shop/shop_screen.dart';

class HomeScreen extends ConsumerStatefulWidget {
  const HomeScreen({super.key});

  @override
  ConsumerState<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  int _selectedIndex = 0;
  late AnimationController _animationController;

  final List<NavigationItem> _navigationItems = [
    NavigationItem(
      icon: Icons.home,
      label: 'Home',
      activeIcon: Icons.home,
    ),
    NavigationItem(
      icon: Icons.style_outlined,
      label: 'Cards',
      activeIcon: Icons.style,
    ),
    NavigationItem(
      icon: Icons.directions_car_outlined,
      label: 'Rally',
      activeIcon: Icons.directions_car,
    ),
    NavigationItem(
      icon: Icons.store_outlined,
      label: 'Shop',
      activeIcon: Icons.store,
    ),
    NavigationItem(
      icon: Icons.person_outline,
      label: 'Profile',
      activeIcon: Icons.person,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      body: AnimatedSwitcher(
        duration: const Duration(milliseconds: 300),
        child: _buildCurrentPage(),
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  Widget _buildCurrentPage() {
    switch (_selectedIndex) {
      case 0:
        return const HomeTab();
      case 1:
        return const CardCollectionScreen();
      case 2:
        return const RallyScreen();
      case 3:
        return const ShopScreen();
      case 4:
        return const ProfileTab();
      default:
        return const HomeTab();
    }
  }

  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: _navigationItems.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = _selectedIndex == index;

              return GestureDetector(
                onTap: () => _onItemTapped(index),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected 
                      ? AppTheme.primaryColor.withOpacity(0.1)
                      : Colors.transparent,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        isSelected ? item.activeIcon : item.icon,
                        color: isSelected 
                          ? AppTheme.primaryColor 
                          : Colors.white54,
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        item.label,
                        style: TextStyle(
                          fontSize: 12,
                          fontWeight: isSelected 
                            ? FontWeight.w600 
                            : FontWeight.w400,
                          color: isSelected 
                            ? AppTheme.primaryColor 
                            : Colors.white54,
                        ),
                      ),
                    ],
                  ),
                ),
              ).animate(target: isSelected ? 1 : 0).scale(
                begin: const Offset(1, 1),
                end: const Offset(1.1, 1.1),
                duration: 200.ms,
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

class NavigationItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;

  NavigationItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
  });
}

// Home Tab
class HomeTab extends ConsumerWidget {
  const HomeTab({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final user = ref.watch(currentUserProvider);

    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(AppConstants.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              _buildHeader(user),
              const SizedBox(height: 24),
              
              // Currency Display
              _buildCurrencyDisplay(user),
              const SizedBox(height: 24),
              
              // Quick Actions
              _buildQuickActions(),
              const SizedBox(height: 24),
              
              // Recent Activity
              _buildRecentActivity(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(user) {
    return Row(
      children: [
        Container(
          width: 60,
          height: 60,
          decoration: const BoxDecoration(
            shape: BoxShape.circle,
            gradient: LinearGradient(
              colors: [AppTheme.primaryColor, AppTheme.accentColor],
            ),
          ),
          child: Center(
            child: Text(
              user?.username.substring(0, 1).toUpperCase() ?? 'U',
              style: AppTheme.headingMedium.copyWith(
                color: Colors.white,
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Welcome back,',
                style: AppTheme.bodyMedium.copyWith(
                  color: Colors.white70,
                ),
              ),
              Text(
                user?.username ?? 'Driver',
                style: AppTheme.headingMedium.copyWith(
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ),
        IconButton(
          onPressed: () {
            // TODO: Show notifications
          },
          icon: const Icon(
            Icons.notifications_outlined,
            color: Colors.white70,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencyDisplay(user) {
    return Row(
      children: [
        Expanded(
          child: _buildCurrencyCard(
            icon: Icons.monetization_on,
            label: 'Coins',
            value: user?.coins.toString() ?? '0',
            color: AppTheme.warningColor,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildCurrencyCard(
            icon: Icons.diamond,
            label: 'Gems',
            value: user?.gems.toString() ?? '0',
            color: AppTheme.primaryColor,
          ),
        ),
      ],
    );
  }

  Widget _buildCurrencyCard({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(AppConstants.borderRadius),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: AppTheme.bodySmall.copyWith(
                  color: Colors.white70,
                ),
              ),
              Text(
                value,
                style: AppTheme.headingSmall.copyWith(
                  color: Colors.white,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: AppTheme.headingSmall.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: PrimaryButton(
                text: 'Join Rally',
                icon: Icons.directions_car,
                onPressed: () {
                  // TODO: Navigate to rally selection
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: SecondaryButton(
                text: 'Open Pack',
                icon: Icons.card_giftcard,
                onPressed: () {
                  // TODO: Navigate to shop
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Recent Activity',
          style: AppTheme.headingSmall.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppTheme.darkCard,
            borderRadius: BorderRadius.circular(AppConstants.borderRadius),
          ),
          child: Center(
            child: Text(
              'No recent activity',
              style: AppTheme.bodyMedium.copyWith(
                color: Colors.white54,
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// Placeholder tabs
class CardsTab extends StatelessWidget {
  const CardsTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Text(
          'Cards Tab - Coming Soon',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}

class RallyTab extends StatelessWidget {
  const RallyTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Text(
          'Rally Tab - Coming Soon',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}

class ShopTab extends StatelessWidget {
  const ShopTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Text(
          'Shop Tab - Coming Soon',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Text(
          'Profile Tab - Coming Soon',
          style: TextStyle(color: Colors.white),
        ),
      ),
    );
  }
}
